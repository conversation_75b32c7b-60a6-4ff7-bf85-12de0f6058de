# Quiz Answer Details API Example

## Get Quiz Answer Details by ID

This endpoint returns all details for a specific quiz answer, including student information, quiz information, and the answers provided.

### Endpoint

```
GET /quiz-answers/:id
```

### Example Request

```
GET /quiz-answers/5
```

### Example Response

```json
{
  "message": "Quiz answer details retrieved successfully",
  "data": {
    "id": 5,
    "quizId": 3,
    "quizName": "test2",
    "quizType": "Week",
    "studentId": 1,
    "studentName": "<PERSON>",
    "studentCode": "ST12345",
    "studentEmail": "<EMAIL>",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 800,
    "timeLimit": 30,
    "attemptNumber": 1,
    "maxAttempts": 1,
    "submissionDate": "2025-05-22T19:15:38.476Z",
    "autoGraded": true,
    "grade": 9,
    "finalGrade": 9,
    "answers": [
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 1,
        "userAnswer": "ds",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "gfds",
        "questionGrade": 3,
        "userAnswer": "gh",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "hgfdsa",
        "questionGrade": 5,
        "userAnswer": "gh",
        "isCorrect": true
      }
    ]
  }
}
```

## Notes

1. This endpoint provides comprehensive details about a quiz answer, including:
   - Quiz information (name, type, time limit, etc.)
   - Student information (name, code, email)
   - Semester, lesson, week, and subject information
   - Submission details (time taken, attempt number, submission date)
   - Grading information (auto-graded status, grade, final grade)
   - Complete list of answers with questions and correctness status

2. The `answers` array contains all the answers submitted by the student, with each answer including:
   - The question text
   - The question grade value
   - The user's answer
   - For MCQ questions, whether the answer is correct

3. This endpoint is useful for:
   - Displaying detailed quiz results to students
   - Allowing teachers to review and grade quiz answers
   - Generating reports on student performance
