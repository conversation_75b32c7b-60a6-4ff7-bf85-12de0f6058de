import { IsInt, IsNotEmpty, IsOptional, IsString, IsArray, ValidateNested } from 'class-validator';
import { Type } from 'class-transformer';
import { SubCategory } from './subcategory.dto';

export class CreateSubjectDto {
  @IsNotEmpty({ message: 'Subject name is required' })
  @IsString({ message: 'Name must be a string' })
  name: string;

  @IsNotEmpty({ message: 'Subject code is required' })
  @IsString({ message: 'Code must be a string' })
  code: string;

  @IsNotEmpty({ message: 'Semester template ID is required' })
  @IsInt({ message: 'Semester template ID must be an integer' })
  semesterTemplateId: number;

  @IsOptional()
  @IsInt({ message: 'Teacher ID must be an integer' })
  teacherId?: number;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SubCategory)
  subCategories?: SubCategory[];
}

// AddSubjectToTemplateDto has been removed as it's now merged into CreateSubjectDto
