# Quiz Status Implementation in getSubjectsAndLessonsByWeekId

## Overview

The `getSubjectsAndLessonsByWeekId` endpoint now includes quiz status information based on the student's attempts and grades. The status can be one of the following:

1. **pending**: The last attempt has grade === null (waiting for manual grading)
2. **pass**: The last attempt has grade >= 50% of finalGrade
3. **fail**: The last attempt has grade < 50% of finalGrade
4. **excellent**: The last attempt has grade === finalGrade (perfect score)
5. **closed**: The week's end date is in the past and the student has no attempts
6. **redo**: The quiz is either closed or failed with max attempts reached, but has an entry in the Redo table
7. **open**: Default status for quizzes that don't match any of the above conditions

## Example Request

```
GET /semesters/week/2/subjects-lessons?studentId=5
```

## Example Response

```json
{
  "message": "Subjects, lessons and quizzes retrieved successfully",
  "data": {
    "subjects": [
      {
        "id": 1,
        "name": "الحان",
        "code": "123",
        "lessons": [
          {
            "id": 1,
            "name": "lesson1",
            "items": [],
            "quizzes": [
              {
                "id": 1,
                "name": "test exam",
                "timeLimit": 30,
                "grade": 9,
                "numberOfAttempts": 2,
                "type": "Week",
                "lessonId": 1,
                "order": 0,
                "userAttempts": 1,
                "status": "pending"
              },
              {
                "id": 2,
                "name": "test2",
                "timeLimit": 30,
                "grade": 9,
                "numberOfAttempts": 1,
                "type": "Week",
                "lessonId": 1,
                "order": 1,
                "userAttempts": 1,
                "status": "pass"
              },
              {
                "id": 3,
                "name": "test3",
                "timeLimit": 30,
                "grade": 10,
                "numberOfAttempts": 1,
                "type": "Week",
                "lessonId": 1,
                "order": 2,
                "userAttempts": 1,
                "status": "excellent"
              },
              {
                "id": 4,
                "name": "test4",
                "timeLimit": 30,
                "grade": 10,
                "numberOfAttempts": 1,
                "type": "Week",
                "lessonId": 1,
                "order": 3,
                "userAttempts": 1,
                "status": "fail"
              },
              {
                "id": 5,
                "name": "test5",
                "timeLimit": 30,
                "grade": 10,
                "numberOfAttempts": 1,
                "type": "Week",
                "lessonId": 1,
                "order": 4,
                "userAttempts": 0,
                "status": "closed"
              },
              {
                "id": 6,
                "name": "test6",
                "timeLimit": 30,
                "grade": 10,
                "numberOfAttempts": 1,
                "type": "Week",
                "lessonId": 1,
                "order": 5,
                "userAttempts": 1,
                "status": "redo"
              }
            ],
            "order": 1
          }
        ]
      }
    ]
  }
}
```

## Status Determination Logic

```typescript
// Determine status based on the latest attempt
if (latestAttempt.grade === null) {
  quizStatusMap[quizId] = 'pending';
} else {
  const finalGrade = latestAttempt.finalGrade;
  const grade = latestAttempt.grade;

  if (grade === finalGrade) {
    quizStatusMap[quizId] = 'excellent';
  } else if (grade >= finalGrade * 0.5) {
    quizStatusMap[quizId] = 'pass';
  } else {
    quizStatusMap[quizId] = 'fail';
  }
}

// For quizzes with no attempts, check if the week is closed
const currentDate = new Date();
if (week.endDate < currentDate) {
  allQuizIds.forEach(quizId => {
    if (!quizAttemptsMap[quizId]) {
      quizStatusMap[quizId] = 'closed';
      quizAttemptsMap[quizId] = 0;
    }
  });
}

// Check for quizzes that need to be checked in the Redo table
const quizzesToCheck = [];

// Collect quizzes that are either closed or failed with max attempts reached
allQuizIds.forEach(quizId => {
  const numId = Number(quizId);
  const status = quizStatusMap[quizId];
  const attempts = quizAttemptsMap[quizId] || 0;

  // Find the quiz to get numberOfAttempts
  const quiz = quizzes.find(q => q.id === numId);

  if (status === 'closed' || (status === 'fail' && attempts >= quiz?.numberOfAttempts)) {
    quizzesToCheck.push(numId);
  }
});

// If there are quizzes to check, query the Redo table
if (quizzesToCheck.length > 0) {
  const redoEntries = await this.prisma.redo.findMany({
    where: {
      quizId: {
        in: quizzesToCheck
      },
      studentId: studentId
    }
  });

  // Update status to 'redo' for quizzes that have a redo entry
  redoEntries.forEach(redo => {
    quizStatusMap[redo.quizId] = 'redo';
  });
}
```

## Status Color Mapping (Frontend Reference)

Here's a suggested color mapping for the frontend:

- **pending**: Orange/Amber (#FFA500)
- **pass**: Green (#4CAF50)
- **fail**: Red (#F44336)
- **excellent**: Gold (#FFD700)
- **closed**: Gray (#9E9E9E)
- **open**: Blue (#2196F3)
- **redo**: Purple (#9C27B0)
