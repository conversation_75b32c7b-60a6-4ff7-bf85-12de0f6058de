# Quiz Answer API Examples

## Submit Quiz Answer

### Request

```<PERSON>son
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 1,
  "timeTaken": 300,
  "answers": [
    {
      "type": "mcq",
      "questionId": 1,
      "selectedAnswerId": 0
    },
    {
      "type": "text",
      "questionId": 2,
      "text": "This is my answer to the text question"
    },
    {
      "type": "record",
      "questionId": 3,
      "recordingUrl": "https://example.com/recording.mp3"
    }
  ]
}
```

### Response

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 1,
    "quizId": 1,
    "quizName": "امتحان مهم",
    "studentId": 5,
    "studentName": "<PERSON>e",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 3,
    "lessonName": "Introduction to Christian Theology",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "Theology",
    "timeTaken": 300,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T16:51:55.000Z",
    "autoGraded": true,
    "grade": 15,
    "finalGrade": 75,
    "maxGrade": 20,
    "answers": [
      {
        "type": "mcq",
        "questionId": 1,
        "selectedAnswerId": 0,
        "questionText": "فاضلنا قد ايه و نخلص",
        "questionGrade": 15,
        "selectedAnswerText": "شهر",
        "correctAnswerId": 0,
        "isCorrect": true
      },
      {
        "type": "text",
        "questionId": 2,
        "text": "This is my answer to the text question",
        "questionText": "بتحب مارك ؟",
        "questionGrade": 5,
        "correctAnswer": "لا"
      },
      {
        "type": "record",
        "questionId": 3,
        "recordingUrl": "https://example.com/recording.mp3",
        "questionText": "Record your answer",
        "questionGrade": 5
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 15,
      "totalPossiblePoints": 15,
      "totalQuizPoints": 20,
      "percentageScore": 75
    }
  }
}
```

## Notes on the Implementation

1. **Auto-Grading**:
   - MCQ questions are automatically graded by comparing the selected answer ID with the correct answer ID
   - Text and recording questions require manual grading

2. **Enhanced Answer Structure**:
   - Each answer includes the original question text and grade
   - MCQ answers include the selected answer text, correct answer ID, and whether the answer is correct
   - Text answers include the correct answer (if available) for reference during grading

3. **Required Fields**:
   - The API automatically fetches and sets the following fields:
     - studentId (from the JWT token)
     - subjectId (from the quiz)
     - lessonId (from the quiz)
     - semesterId (from the current active semester)
     - weekId (from the current active week)

4. **Grading Summary**:
   - totalAutoGradedPoints: Points earned from correctly answered MCQ questions
   - totalPossiblePoints: Maximum possible points from MCQ questions
   - totalQuizPoints: Total points for the entire quiz
   - percentageScore: Percentage of points earned from auto-graded questions

5. **Multiple Attempts**:
   - The system tracks the number of attempts for each student on each quiz
   - The attemptNumber field indicates which attempt this submission represents
   - Students are prevented from exceeding the quiz's configured number of attempts
