# Quiz Answer API Documentation

This document provides examples of requests and responses for the newly implemented quiz answer and grading APIs.

## Table of Contents

1. [Submit Quiz Answer](#1-submit-quiz-answer-post-quiz-answers)
2. [Submit Quiz Answer with Auto-Grading](#2-submit-quiz-answer-with-auto-grading-post-quiz-answersauto-grade)
3. [Get Quiz Answer by ID](#3-get-quiz-answer-by-id-get-quiz-answersid)
4. [Get Quiz Answers by Quiz ID](#4-get-quiz-answers-by-quiz-id-get-quiz-answersquizquizid)
5. [Get Quiz Answers by Student ID](#5-get-quiz-answers-by-student-id-get-quiz-answersstudentstudentid)
6. [Grade Quiz Answer](#6-grade-quiz-answer-put-quiz-answersidgrade)
7. [Get Simple Quizzes by Lesson ID](#7-get-simple-quizzes-by-lesson-id-get-quizlessonlessonidsimple)
8. [Get Quiz with Random Questions](#8-get-quiz-with-random-questions-get-quizidrandom)
9. [Notes on Implementation](#notes-on-implementation)

## 1. Submit Quiz Answer (POST /quiz-answers)

Submit a student's answers to a quiz.

### Request

```json
POST /quiz-answers
Authorization: Bearer {jwt_token}

{
  "quizId": 1,
  "answers": [
    {
      "type": "mcq",
      "questionId": 1,
      "selectedAnswerId": 2
    },
    {
      "type": "text",
      "questionId": 2,
      "text": "This is my answer to the text question"
    },
    {
      "type": "record",
      "questionId": 3,
      "recordingUrl": "https://example.com/recordings/abc123.mp3"
    }
  ]
}
```

### Response

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 1,
    "studentId": 5,
    "quizId": 1,
    "answers": [
      {
        "type": "mcq",
        "questionId": 1,
        "selectedAnswerId": 2
      },
      {
        "type": "text",
        "questionId": 2,
        "text": "This is my answer to the text question"
      },
      {
        "type": "record",
        "questionId": 3,
        "recordingUrl": "https://example.com/recordings/abc123.mp3"
      }
    ],
    "grade": null,
    "createdAt": "2024-05-15T14:30:00.000Z",
    "updatedAt": "2024-05-15T14:30:00.000Z",
    "quiz": {
      "name": "Introduction to Theology",
      "grade": 100
    },
    "student": {
      "studentCode": "STU5",
      "user": {
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    }
  }
}
```

## 2. Submit Quiz Answer with Auto-Grading (POST /quiz-answers/auto-grade)

Submit a student's answers to a quiz with automatic grading for MCQ questions.

### Request

```json
POST /quiz-answers/auto-grade
Authorization: Bearer {jwt_token}

{
  "quizId": 1,
  "timeTaken": 300,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "selectedAnswerId": 0
    },
    {
      "type": "text",
      "questionId": 1,
      "text": "This is my answer to the text question"
    },
    {
      "type": "record",
      "questionId": 2,
      "recordingUrl": "https://example.com/recordings/abc123.mp3"
    }
  ]
}
```

### Response

```json
{
  "message": "Quiz answers submitted and auto-graded successfully",
  "data": {
    "id": 1,
    "studentId": 5,
    "quizId": 1,
    "answers": [
      {
        "type": "mcq",
        "questionId": 0,
        "selectedAnswerId": 0
      },
      {
        "type": "text",
        "questionId": 1,
        "text": "This is my answer to the text question"
      },
      {
        "type": "record",
        "questionId": 2,
        "recordingUrl": "https://example.com/recordings/abc123.mp3"
      }
    ],
    "grade": 15,
    "timeTaken": 300,
    "autoGraded": true,
    "createdAt": "2024-05-15T14:30:00.000Z",
    "updatedAt": "2024-05-15T14:30:00.000Z",
    "quiz": {
      "name": "Introduction to Theology",
      "grade": 20
    },
    "student": {
      "studentCode": "STU5",
      "user": {
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    },
    "autoGradingDetails": {
      "totalCorrectPoints": 15,
      "maxPossiblePoints": 20,
      "percentageCorrect": 75,
      "finalGrade": 15
    }
  }
}
```

## 3. Get Quiz Answer by ID (GET /quiz-answers/:id)

Retrieve a specific quiz answer with questions and student answers combined.

### Request

```
GET /quiz-answers/1
Authorization: Bearer {jwt_token}
```

### Response

```json
{
  "message": "Quiz answer retrieved successfully",
  "data": {
    "id": 1,
    "quizId": 1,
    "studentId": 5,
    "grade": null,
    "createdAt": "2024-05-15T14:30:00.000Z",
    "updatedAt": "2024-05-15T14:30:00.000Z",
    "quiz": {
      "id": 1,
      "name": "Introduction to Theology",
      "grade": 100
    },
    "student": {
      "id": 5,
      "studentCode": "STU5",
      "user": {
        "id": 10,
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    },
    "combinedData": [
      {
        "question": {
          "id": 1,
          "question": "Who is the author of the Gospel of John?",
          "type": "mcq",
          "grade": 25,
          "answers": [
            {"id": 1, "text": "Matthew"},
            {"id": 2, "text": "John"},
            {"id": 3, "text": "Luke"},
            {"id": 4, "text": "Mark"}
          ],
          "correctAnswerId": 2
        },
        "studentAnswer": {
          "type": "mcq",
          "questionId": 1,
          "selectedAnswerId": 2
        }
      },
      {
        "question": {
          "id": 2,
          "question": "Explain the concept of Trinity.",
          "type": "text",
          "grade": 50
        },
        "studentAnswer": {
          "type": "text",
          "questionId": 2,
          "text": "This is my answer to the text question"
        }
      },
      {
        "question": {
          "id": 3,
          "question": "Recite the Lord's Prayer.",
          "type": "record",
          "grade": 25
        },
        "studentAnswer": {
          "type": "record",
          "questionId": 3,
          "recordingUrl": "https://example.com/recordings/abc123.mp3"
        }
      }
    ]
  }
}
```

## 4. Get Quiz Answers by Quiz ID (GET /quiz-answers/quiz/:quizId)

Retrieve all student answers for a specific quiz.

### Request

```
GET /quiz-answers/quiz/1
Authorization: Bearer {jwt_token}
```

### Response

```json
{
  "message": "Quiz answers retrieved successfully",
  "data": [
    {
      "id": 1,
      "studentId": 5,
      "quizId": 1,
      "answers": [
        {
          "type": "mcq",
          "questionId": 1,
          "selectedAnswerId": 2
        },
        {
          "type": "text",
          "questionId": 2,
          "text": "This is my answer to the text question"
        },
        {
          "type": "record",
          "questionId": 3,
          "recordingUrl": "https://example.com/recordings/abc123.mp3"
        }
      ],
      "grade": null,
      "createdAt": "2024-05-15T14:30:00.000Z",
      "updatedAt": "2024-05-15T14:30:00.000Z",
      "student": {
        "id": 5,
        "studentCode": "STU5",
        "user": {
          "id": 10,
          "name": "John Doe",
          "email": "<EMAIL>"
        }
      }
    },
    {
      "id": 2,
      "studentId": 6,
      "quizId": 1,
      "answers": [
        {
          "type": "mcq",
          "questionId": 1,
          "selectedAnswerId": 1
        },
        {
          "type": "text",
          "questionId": 2,
          "text": "Another student's answer to the text question"
        },
        {
          "type": "record",
          "questionId": 3,
          "recordingUrl": "https://example.com/recordings/def456.mp3"
        }
      ],
      "grade": 75,
      "createdAt": "2024-05-15T15:00:00.000Z",
      "updatedAt": "2024-05-15T16:30:00.000Z",
      "student": {
        "id": 6,
        "studentCode": "STU6",
        "user": {
          "id": 11,
          "name": "Jane Smith",
          "email": "<EMAIL>"
        }
      }
    }
  ]
}
```

## 5. Get Quiz Answers by Student ID (GET /quiz-answers/student/:studentId)

Retrieve all quiz answers submitted by a specific student.

### Request

```
GET /quiz-answers/student/5
Authorization: Bearer {jwt_token}
```

### Response

```json
{
  "message": "Quiz answers retrieved successfully",
  "data": [
    {
      "id": 1,
      "studentId": 5,
      "quizId": 1,
      "answers": [
        {
          "type": "mcq",
          "questionId": 1,
          "selectedAnswerId": 2
        },
        {
          "type": "text",
          "questionId": 2,
          "text": "This is my answer to the text question"
        },
        {
          "type": "record",
          "questionId": 3,
          "recordingUrl": "https://example.com/recordings/abc123.mp3"
        }
      ],
      "grade": null,
      "createdAt": "2024-05-15T14:30:00.000Z",
      "updatedAt": "2024-05-15T14:30:00.000Z",
      "quiz": {
        "id": 1,
        "name": "Introduction to Theology",
        "grade": 100
      }
    },
    {
      "id": 3,
      "studentId": 5,
      "quizId": 2,
      "answers": [
        {
          "type": "mcq",
          "questionId": 4,
          "selectedAnswerId": 3
        },
        {
          "type": "text",
          "questionId": 5,
          "text": "Answer to another quiz question"
        }
      ],
      "grade": 85,
      "createdAt": "2024-05-16T10:15:00.000Z",
      "updatedAt": "2024-05-16T11:30:00.000Z",
      "quiz": {
        "id": 2,
        "name": "Church History",
        "grade": 100
      }
    }
  ]
}
```

## 6. Grade Quiz Answer (PUT /quiz-answers/:id/grade)

Grade a student's quiz submission.

### Request

```json
PUT /quiz-answers/1/grade
Authorization: Bearer {jwt_token}

{
  "grade": 85
}
```

### Response

```json
{
  "message": "Quiz answer graded successfully",
  "data": {
    "id": 1,
    "studentId": 5,
    "quizId": 1,
    "answers": [
      {
        "type": "mcq",
        "questionId": 1,
        "selectedAnswerId": 2
      },
      {
        "type": "text",
        "questionId": 2,
        "text": "This is my answer to the text question"
      },
      {
        "type": "record",
        "questionId": 3,
        "recordingUrl": "https://example.com/recordings/abc123.mp3"
      }
    ],
    "grade": 85,
    "createdAt": "2024-05-15T14:30:00.000Z",
    "updatedAt": "2024-05-15T17:45:00.000Z",
    "quiz": {
      "name": "Introduction to Theology",
      "grade": 100
    },
    "student": {
      "studentCode": "STU5",
      "user": {
        "name": "John Doe",
        "email": "<EMAIL>"
      }
    }
  }
}
```

## 7. Get Simple Quizzes by Lesson ID (GET /quiz/lesson/:lessonId/simple)

Retrieve a simplified list of quizzes for a specific lesson.

### Request

```
GET /quiz/lesson/1/simple
```

### Curl Example

```bash
# First, get a JWT token
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | sed 's/"access_token":"//')

# Get quizzes for lesson ID 1
curl -X GET http://localhost:8080/quiz/lesson/1/simple \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

### PowerShell Example

```powershell
# Get JWT token (as shown in previous examples)

# Get quizzes for lesson ID 1
$response = Invoke-RestMethod -Method GET -Uri "http://localhost:8080/quiz/lesson/1/simple" `
  -Headers @{
    "Authorization" = "Bearer $token"
  }

# Display quiz information
foreach ($quiz in $response.data) {
    Write-Host "Quiz ID: $($quiz.id)"
    Write-Host "Name: $($quiz.name)"
    Write-Host "Grade: $($quiz.grade)"
    Write-Host "Type: $($quiz.type)"
    Write-Host "Time Limit: $($quiz.timeLimit) minutes"
    Write-Host "Number of Attempts: $($quiz.numberOfAttempts)"
    Write-Host "Subject: $($quiz.subject.name) ($($quiz.subject.code))"
}
```

### Response

```json
{
  "message": "Quizzes retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "امتحان مهم",
      "grade": 20,
      "type": "Week",
      "timeLimit": 40,
      "numberOfAttempts": 2,
      "subject": {
        "id": 1,
        "name": "Introduction to Theology",
        "code": "THEO101"
      }
    },
    {
      "id": 3,
      "name": "امتحان مهم",
      "grade": 5,
      "type": "Week",
      "timeLimit": 40,
      "numberOfAttempts": 1,
      "subject": {
        "id": 1,
        "name": "Introduction to Theology",
        "code": "THEO101"
      }
    }
  ]
}
```

## 8. Get Quiz with Random Questions (GET /quiz/:id/random)

Retrieve a quiz with all its data, including randomized questions where the total question grades equal the quiz's total grade.

### Request

```
GET /quiz/1/random
Authorization: Bearer {jwt_token}
```

### Curl Example

```bash
# First, get a JWT token
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | sed 's/"access_token":"//')

# Get quiz with random questions
curl -X GET http://localhost:8080/quiz/1/random \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

### PowerShell Example

```powershell
# Get JWT token (as shown in previous examples)

# Get quiz with random questions
$response = Invoke-RestMethod -Method GET -Uri "http://localhost:8080/quiz/1/random" `
  -Headers @{
    "Authorization" = "Bearer $token"
  }

# Display quiz information
Write-Host "Quiz ID: $($response.data.id)"
Write-Host "Name: $($response.data.name)"
Write-Host "Total Grade: $($response.data.grade)"
Write-Host "Number of Questions: $($response.data.content.Count)"
```

### Response

```json
{
  "message": "Quiz retrieved successfully",
  "data": {
    "id": 1,
    "name": "امتحان مهم",
    "subjectId": 1,
    "semesterId": 1,
    "grade": 8,
    "createdAt": "2024-03-31T07:57:34.000Z",
    "updatedAt": "2024-03-31T07:57:34.000Z",
    "type": "Week",
    "numberOfAttempts": 2,
    "timeLimit": 40,
    "lessonId": 1,
    "subject": {
      "id": 1,
      "name": "Introduction to Theology",
      "code": "THEO101",
      "semesterTemplateId": 1,
      "teacherId": 1,
      "createdAt": "2024-03-29T13:23:31.000Z",
      "updatedAt": "2024-03-29T13:23:31.000Z"
    },
    "semester": {
      "id": 1,
      "semesterTemplateId": 1,
      "year": 2024,
      "name": "Spring 2024",
      "startDate": "2024-01-15T00:00:00.000Z",
      "endDate": "2024-05-15T00:00:00.000Z",
      "isCurrent": true,
      "createdAt": "2024-03-29T13:23:31.000Z",
      "updatedAt": "2024-03-29T13:23:31.000Z"
    },
    "lesson": {
      "id": 1,
      "name": "Introduction to Christian Theology",
      "subjectId": 1,
      "createdAt": "2024-03-29T13:23:31.000Z"
    },
    "content": [
      {
        "question": "Medium difficulty question?",
        "type": "text",
        "grade": 3,
        "correctAnswer": "Sample answer"
      },
      {
        "question": "Hard difficulty question?",
        "type": "mcq",
        "grade": 5,
        "answers": [
          {"id": 0, "text": "Option A"},
          {"id": 1, "text": "Option B"},
          {"id": 2, "text": "Option C"},
          {"id": 3, "text": "Option D"}
        ],
        "correctAnswerId": 2
      }
    ]
  }
}
```

### Features

1. **Categorized Question Selection**: Questions are selected from three grade categories:
   - Grade 1 questions (worth 1 point each)
   - Grade 3 questions (worth 3 points each)
   - Grade 5 questions (worth 5 points each)

2. **Optimal Grade Matching**: The algorithm selects questions to match the quiz's total grade:
   - For example, if the quiz grade is 8, it will select one grade 5 question and one grade 3 question
   - If the quiz grade is 13, it will select two grade 5 questions and one grade 3 question
   - If the quiz grade is 14, it will select two grade 5 questions, one grade 3 question, and one grade 1 question

3. **Randomized Selection**: Questions are randomly selected from each grade category, ensuring variety in the quiz content.

4. **Fallback Combinations**: If the exact grade can't be matched with the standard combinations, the algorithm tries alternative combinations:
   - Replacing a grade 3 question with three grade 1 questions
   - Replacing a grade 5 question with one grade 3 question and two grade 1 questions

5. **Complete Quiz Data**: The response includes all quiz data, including subject, semester, and lesson information.

## Notes on Implementation

1. **Authentication**:
   - All endpoints require JWT authentication
   - The token must be included in the Authorization header

2. **Student Submission**:
   - When a student submits answers, the system automatically associates the submission with their student ID
   - Students can only submit one set of answers per quiz

3. **Grading**:
   - The grade value must be a non-negative integer
   - The grade cannot exceed the maximum grade defined for the quiz

4. **Error Handling**:
   - If a quiz or student is not found, a 404 Not Found response is returned
   - If a student tries to submit answers for a quiz they've already answered, a 400 Bad Request is returned
   - If an invalid grade is provided, a 400 Bad Request is returned

5. **Database Schema**:
   - The implementation adds a new `QuizAnswer` model to the database schema
   - This model stores the relationship between students, quizzes, and their answers
   - The grade field is optional and is only populated after grading

## Example API Test Scripts

### PowerShell Script for Testing API

We've created a PowerShell script to test all the API endpoints. This script:
1. Logs in to get a JWT token
2. Submits answers for Quiz ID 1 and 3
3. Grades the submitted answers
4. Retrieves answers by quiz ID and student ID

```powershell
# PowerShell script to test the Quiz Answer API

# Function to make API requests
function Invoke-ApiRequest {
    param (
        [string]$Method,
        [string]$Endpoint,
        [string]$Token,
        [object]$Body
    )

    $headers = @{
        "Content-Type" = "application/json"
    }

    if ($Token) {
        $headers["Authorization"] = "Bearer $Token"
    }

    $params = @{
        Method = $Method
        Uri = "http://localhost:8080$Endpoint"
        Headers = $headers
    }

    if ($Body) {
        $params["Body"] = ($Body | ConvertTo-Json -Depth 10)
    }

    try {
        $response = Invoke-RestMethod @params
        return $response
    }
    catch {
        Write-Host "Error: $_"
        Write-Host "Status Code: $($_.Exception.Response.StatusCode.value__)"
        try {
            $reader = New-Object System.IO.StreamReader($_.Exception.Response.GetResponseStream())
            $reader.BaseStream.Position = 0
            $reader.DiscardBufferedData()
            $responseBody = $reader.ReadToEnd()
            Write-Host "Response Body: $responseBody"
        }
        catch {
            Write-Host "Could not read response body: $_"
        }
        return $null
    }
}

# Login to get JWT token
Write-Host "Logging in to get JWT token..."
$loginBody = @{
    email = "<EMAIL>"
    password = "password123"
}

$loginResponse = Invoke-ApiRequest -Method "POST" -Endpoint "/auth/login" -Body $loginBody

if (-not $loginResponse) {
    Write-Host "Login failed. Using dummy token for testing..."
    $token = "dummy_token"
}
else {
    $token = $loginResponse.access_token
    Write-Host "Successfully logged in and got token."
}

# Submit answers for Quiz ID 1
Write-Host "`nSubmitting answers for Quiz ID 1..."
$quiz1AnswersBody = @{
    quizId = 1
    answers = @(
        @{
            type = "mcq"
            questionId = 0
            selectedAnswerId = 0
        },
        @{
            type = "text"
            questionId = 1
            text = "نعم، بحب مارك جدا"
        }
    )
}

$submitQuiz1Response = Invoke-ApiRequest -Method "POST" -Endpoint "/quiz-answers" -Token $token -Body $quiz1AnswersBody
if ($submitQuiz1Response) {
    Write-Host "Successfully submitted answers for Quiz ID 1."
    $quizAnswer1Id = $submitQuiz1Response.data.id
    Write-Host "Quiz Answer ID: $quizAnswer1Id"
}

# Grade the quiz answer
if ($quizAnswer1Id) {
    Write-Host "`nGrading Quiz Answer ID $quizAnswer1Id..."
    $gradeBody = @{
        grade = 18
    }

    $gradeResponse = Invoke-ApiRequest -Method "PUT" -Endpoint "/quiz-answers/$quizAnswer1Id/grade" -Token $token -Body $gradeBody
    if ($gradeResponse) {
        Write-Host "Successfully graded Quiz Answer ID $quizAnswer1Id."
    }
}

# Get quiz answers by quiz ID
Write-Host "`nGetting Quiz Answers for Quiz ID 1..."
$quizAnswersResponse = Invoke-ApiRequest -Method "GET" -Endpoint "/quiz-answers/quiz/1" -Token $token
if ($quizAnswersResponse) {
    Write-Host "Successfully retrieved Quiz Answers for Quiz ID 1."
    Write-Host "Number of answers: $($quizAnswersResponse.data.Count)"
}
```

### Curl Commands (for Linux/Mac/Git Bash)

If you're using Linux, Mac, or Git Bash on Windows, you can use these curl commands:

#### Submit Quiz Answers

```bash
#!/bin/bash

# First, let's get a JWT token by logging in
echo "Logging in to get JWT token..."
TOKEN_RESPONSE=$(curl -s -X POST http://localhost:8080/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }')

# Extract the access token from the response
ACCESS_TOKEN=$(echo $TOKEN_RESPONSE | grep -o '"access_token":"[^"]*' | sed 's/"access_token":"//')

echo "Submitting answers for Quiz ID 1..."
curl -X POST http://localhost:8080/quiz-answers \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "quizId": 1,
    "answers": [
      {
        "type": "mcq",
        "questionId": 0,
        "selectedAnswerId": 0
      },
      {
        "type": "text",
        "questionId": 1,
        "text": "نعم، بحب مارك جدا"
      }
    ]
  }'
```

#### Grade Quiz Answer

```bash
#!/bin/bash

# Get JWT token (as shown above)

# Replace QUIZ_ANSWER_ID with the actual ID after submitting answers
QUIZ_ANSWER_ID=1

echo "Grading Quiz Answer ID $QUIZ_ANSWER_ID..."
curl -X PUT http://localhost:8080/quiz-answers/$QUIZ_ANSWER_ID/grade \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer $ACCESS_TOKEN" \
  -d '{
    "grade": 18
  }'
```

#### Get Quiz Answers

```bash
#!/bin/bash

# Get JWT token (as shown above)

# Replace with actual IDs
QUIZ_ANSWER_ID=1
QUIZ_ID=1
STUDENT_ID=1

echo "Getting Quiz Answer by ID $QUIZ_ANSWER_ID..."
curl -X GET http://localhost:8080/quiz-answers/$QUIZ_ANSWER_ID \
  -H "Authorization: Bearer $ACCESS_TOKEN"

echo -e "\n\nGetting Quiz Answers for Quiz ID $QUIZ_ID..."
curl -X GET http://localhost:8080/quiz-answers/quiz/$QUIZ_ID \
  -H "Authorization: Bearer $ACCESS_TOKEN"

echo -e "\n\nGetting Quiz Answers for Student ID $STUDENT_ID..."
curl -X GET http://localhost:8080/quiz-answers/student/$STUDENT_ID \
  -H "Authorization: Bearer $ACCESS_TOKEN"
```

## Real Quiz Examples from Database

Based on the actual quizzes in the database:

### Quiz ID 1 (امتحان مهم)
```json
{
  "id": 1,
  "name": "امتحان مهم",
  "grade": 20,
  "content": [
    {
      "question": "فاضلنا قد ايه و نخلص",
      "type": "mcq",
      "grade": 15,
      "answers": [
        {"id": 0, "text": "شهر"},
        {"id": 1, "text": "اسبوع"},
        {"id": 2, "text": "بعد العيد"},
        {"id": 3, "text": "الصيف الجي"}
      ],
      "correctAnswerId": 0
    },
    {
      "question": "بتحب مارك ؟",
      "type": "text",
      "grade": 5,
      "correctAnswer": "لا"
    }
  ]
}
```

### Quiz ID 3 (امتحان مهم)
```json
{
  "id": 3,
  "name": "امتحان مهم",
  "grade": 5,
  "content": [
    {
      "question": "Easy question",
      "type": "mcq",
      "grade": 1,
      "answers": [
        {"id": 0, "text": "yes"},
        {"id": 1, "text": "no"}
      ],
      "correctAnswerId": 0
    },
    {
      "question": "medium question",
      "type": "text",
      "grade": 3
    },
    {
      "question": "Hard question",
      "type": "text",
      "grade": 5
    }
  ]
}
```

## Setup and Installation

To use the Quiz Answer API, follow these steps:

1. **Apply Database Migration**:
   - Run the `Apply-Migration.ps1` script to add the QuizAnswer model to your database
   - This script will handle schema drift issues and give you options to resolve them

   ```powershell
   .\Apply-Migration.ps1
   ```

2. **Start the Server**:
   - Run the `Start-Server.ps1` script to check if the server is running and start it if needed
   - This script will wait for the server to start before completing

   ```powershell
   .\Start-Server.ps1
   ```

3. **Test the API**:
   - Run the `Test-QuizAnswerApi.ps1` script to test all the API endpoints
   - This script will submit answers, grade them, and retrieve them

   ```powershell
   .\Test-QuizAnswerApi.ps1
   ```

This documentation provides examples of how to use the newly implemented quiz answer and grading APIs. The actual response data may vary based on your specific quiz content and student information.
