import { Body, Controller, Get, Param, Post, Put, ParseIntPipe, HttpException, HttpStatus, Logger } from '@nestjs/common';
import { AnnouncementService } from './announcement.service';
import { CreateAnnouncementDto } from './dto/create-announcement.dto';
import { UpdateAnnouncementDto } from './dto/update-announcement.dto';

@Controller('announcement')
export class AnnouncementController {
  private readonly logger = new Logger(AnnouncementController.name);

  constructor(private readonly announcementService: AnnouncementService) {}

  @Post()
  async createAnnouncement(@Body() createAnnouncementDto: CreateAnnouncementDto) {
    try {
      return await this.announcementService.createAnnouncement(createAnnouncementDto);
    } catch (error) {
      this.logger.error(`Error creating announcement: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to create announcement',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('general')
  async getAllGeneralAnnouncements() {
    try {
      return await this.announcementService.getAllGeneralAnnouncements();
    } catch (error) {
      this.logger.error(`Error fetching general announcements: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch general announcements',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('week')
  async getAllWeekAnnouncements() {
    try {
      return await this.announcementService.getAllWeekAnnouncements();
    } catch (error) {
      this.logger.error(`Error fetching week announcements: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch week announcements',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('week/:weekId')
  async getAnnouncementsByWeekId(@Param('weekId', ParseIntPipe) weekId: number) {
    try {
      return await this.announcementService.getAnnouncementsByWeekId(weekId);
    } catch (error) {
      this.logger.error(`Error fetching announcements for week ${weekId}: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch announcements for week',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getAnnouncementById(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.announcementService.getAnnouncementById(id);
    } catch (error) {
      this.logger.error(`Error fetching announcement with ID ${id}: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch announcement',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  async updateAnnouncement(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateAnnouncementDto: UpdateAnnouncementDto,
  ) {
    try {
      return await this.announcementService.updateAnnouncement(id, updateAnnouncementDto);
    } catch (error) {
      this.logger.error(`Error updating announcement with ID ${id}: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to update announcement',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
