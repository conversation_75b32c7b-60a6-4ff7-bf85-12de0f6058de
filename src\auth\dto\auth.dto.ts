import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>otEmpt<PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, IsDateString, Matches, IsOptional, IsUrl } from "class-validator";

export enum UserRole {
  STUDENT = 'student',
  TEACHER = 'teacher'
}

export class RegisterDto {
  @IsEmail({}, { message: 'Invalid email' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;

  @IsNotEmpty({ message: 'Password is required' })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;

  @IsNotEmpty({ message: 'Phone number is required' })
  @IsString()
  @Matches(/^[+]?[(]?[0-9]{3}[)]?[-\s.]?[0-9]{3}[-\s.]?[0-9]{4,6}$/, {
    message: 'Phone number must be in a valid format'
  })
  phone: string;

  @IsNotEmpty({ message: 'Name is required' })
  @IsString()
  name: string;

  @IsNotEmpty({ message: 'Birthday is required' })
  @IsDateString()
  birthday: string;

  @IsNotEmpty({ message: 'Nationality is required' })
  @IsString()
  nationality: string;

  @IsNotEmpty({ message: 'Address is required' })
  @IsString()
  Address: string;

  @IsNotEmpty({ message: 'Gender is required' })
  @IsString()
  gender: string;

  @IsNotEmpty({ message: 'Role is required' })
  @IsEnum(UserRole)
  role: UserRole;

  @IsOptional()
  @IsUrl({}, { message: 'Profile picture must be a valid URL' })
  profilePicture?: string;

  // Student-specific fields
  @IsNotEmpty({ message: 'City is required for students', groups: ['student'] })
  @IsString()
  city?: string;

  @IsNotEmpty({ message: 'Church is required for students', groups: ['student'] })
  @IsString()
  church?: string;

  @IsNotEmpty({ message: 'AbEle3traf is required for students', groups: ['student'] })
  @IsString()
  AbEle3traf?: string;

  @IsNotEmpty({ message: 'Deacon level is required for students', groups: ['student'] })
  @IsString()
  deaconLevel?: string;

  @IsNotEmpty({ message: 'Church service is required for students', groups: ['student'] })
  @IsString()
  churchService?: string;

  // Common fields for both roles
  @IsNotEmpty({ message: 'Qualifications are required' })
  @IsString()
  qualifications: string;

  @IsNotEmpty({ message: 'Personal ID Front is required' })
  @IsString()
  personalIDFront: string;

  @IsNotEmpty({ message: 'Personal ID Back is required' })
  @IsString()
  personalIDBack: string;

  @IsNotEmpty({ message: 'Tazkia is required' })
  @IsString()
  Tazkia: string;
}

export class LoginDto {
  @IsEmail({}, { message: 'Invalid email' })
  @IsNotEmpty({ message: 'Email is required' })
  email: string;
  @IsNotEmpty({ message: 'Password is required' })
  @IsString()
  @MinLength(8, { message: 'Password must be at least 8 characters long' })
  password: string;
}

export class RefreshTokenDto {
  @IsString()
  @IsNotEmpty()
  refresh_token: string;
}
