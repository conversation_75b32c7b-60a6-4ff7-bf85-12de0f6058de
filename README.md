<p align="center">
  <a href="http://nestjs.com/" target="blank"><img src="https://nestjs.com/img/logo-small.svg" width="120" alt="Nest Logo" /></a>
</p>

[circleci-image]: https://img.shields.io/circleci/build/github/nestjs/nest/master?token=abc123def456
[circleci-url]: https://circleci.com/gh/nestjs/nest

  <p align="center">A progressive <a href="http://nodejs.org" target="_blank">Node.js</a> framework for building efficient and scalable server-side applications.</p>
    <p align="center">
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/v/@nestjs/core.svg" alt="NPM Version" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/l/@nestjs/core.svg" alt="Package License" /></a>
<a href="https://www.npmjs.com/~nestjscore" target="_blank"><img src="https://img.shields.io/npm/dm/@nestjs/common.svg" alt="NPM Downloads" /></a>
<a href="https://circleci.com/gh/nestjs/nest" target="_blank"><img src="https://img.shields.io/circleci/build/github/nestjs/nest/master" alt="CircleCI" /></a>
<a href="https://coveralls.io/github/nestjs/nest?branch=master" target="_blank"><img src="https://coveralls.io/repos/github/nestjs/nest/badge.svg?branch=master#9" alt="Coverage" /></a>
<a href="https://discord.gg/G7Qnnhy" target="_blank"><img src="https://img.shields.io/badge/discord-online-brightgreen.svg" alt="Discord"/></a>
<a href="https://opencollective.com/nest#backer" target="_blank"><img src="https://opencollective.com/nest/backers/badge.svg" alt="Backers on Open Collective" /></a>
<a href="https://opencollective.com/nest#sponsor" target="_blank"><img src="https://opencollective.com/nest/sponsors/badge.svg" alt="Sponsors on Open Collective" /></a>
  <a href="https://paypal.me/kamilmysliwiec" target="_blank"><img src="https://img.shields.io/badge/Donate-PayPal-ff3f59.svg" alt="Donate us"/></a>
    <a href="https://opencollective.com/nest#sponsor"  target="_blank"><img src="https://img.shields.io/badge/Support%20us-Open%20Collective-41B883.svg" alt="Support us"></a>
  <a href="https://twitter.com/nestframework" target="_blank"><img src="https://img.shields.io/twitter/follow/nestframework.svg?style=social&label=Follow" alt="Follow us on Twitter"></a>
</p>
  <!--[![Backers on Open Collective](https://opencollective.com/nest/backers/badge.svg)](https://opencollective.com/nest#backer)
  [![Sponsors on Open Collective](https://opencollective.com/nest/sponsors/badge.svg)](https://opencollective.com/nest#sponsor)-->

## Description

[Nest](https://github.com/nestjs/nest) framework TypeScript starter repository.

## Project setup

```bash
$ yarn install
```

## Compile and run the project

```bash
# development
$ yarn run start

# watch mode
$ yarn run start:dev

# production mode
$ yarn run start:prod
```

## Run tests

```bash
# unit tests
$ yarn run test

# e2e tests
$ yarn run test:e2e

# test coverage
$ yarn run test:cov
```

## Deployment

When you're ready to deploy your NestJS application to production, there are some key steps you can take to ensure it runs as efficiently as possible. Check out the [deployment documentation](https://docs.nestjs.com/deployment) for more information.

If you are looking for a cloud-based platform to deploy your NestJS application, check out [Mau](https://mau.nestjs.com), our official platform for deploying NestJS applications on AWS. Mau makes deployment straightforward and fast, requiring just a few simple steps:

```bash
$ yarn install -g mau
$ mau deploy
```

With Mau, you can deploy your application in just a few clicks, allowing you to focus on building features rather than managing infrastructure.

## Resources

Check out a few resources that may come in handy when working with NestJS:

- Visit the [NestJS Documentation](https://docs.nestjs.com) to learn more about the framework.
- For questions and support, please visit our [Discord channel](https://discord.gg/G7Qnnhy).
- To dive deeper and get more hands-on experience, check out our official video [courses](https://courses.nestjs.com/).
- Deploy your application to AWS with the help of [NestJS Mau](https://mau.nestjs.com) in just a few clicks.
- Visualize your application graph and interact with the NestJS application in real-time using [NestJS Devtools](https://devtools.nestjs.com).
- Need help with your project (part-time to full-time)? Check out our official [enterprise support](https://enterprise.nestjs.com).
- To stay in the loop and get updates, follow us on [X](https://x.com/nestframework) and [LinkedIn](https://linkedin.com/company/nestjs).
- Looking for a job, or have a job to offer? Check out our official [Jobs board](https://jobs.nestjs.com).

## Support

Nest is an MIT-licensed open source project. It can grow thanks to the sponsors and support by the amazing backers. If you'd like to join them, please [read more here](https://docs.nestjs.com/support).

## Stay in touch

- Author - [Kamil Myśliwiec](https://twitter.com/kammysliwiec)
- Website - [https://nestjs.com](https://nestjs.com/)
- Twitter - [@nestframework](https://twitter.com/nestframework)

## API Endpoints

### Quiz Answers

#### Get Quiz Answers Summary by Quiz ID
Returns a list of quiz answers for a specific quiz with basic student information.

**Endpoint:** `GET /quiz-answers/quiz/:quizId/summary`

**Example Request:**
```
GET /quiz-answers/quiz/1/summary
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Quiz answers summary retrieved successfully",
  "data": {
    "quiz": {
      "id": 1,
      "name": "Introduction to Theology Quiz"
    },
    "answers": [
      {
        "id": 15,
        "studentName": "John Doe",
        "studentCode": "ST001",
        "attemptNumber": 2,
        "date": "2024-01-15T10:30:00.000Z"
      },
      {
        "id": 23,
        "studentName": "Jane Smith",
        "studentCode": "ST002",
        "attemptNumber": 1,
        "date": "2024-01-14T14:20:00.000Z"
      }
    ]
  }
}
```

### Grades

#### Get Student Summary Grades
Returns comprehensive grade summary for the authenticated student grouped by semester and subjects.

**Endpoint:** `GET /grades/student/summary`

**Example Request:**
```
GET /grades/student/summary
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Student summary grades retrieved successfully",
  "data": {
    "student": {
      "id": 5,
      "name": "John Doe",
      "code": "ST001"
    },
    "semesters": [
      {
        "id": 1,
        "name": "Fall 2024",
        "subjects": [
          {
            "id": 1,
            "name": "Christian Theology",
            "code": "THEO101",
            "finalQuizzes": {
              "totalScore": 85,
              "finalScore": 100,
              "hasQuizzes": true
            },
            "weekQuizzes": {
              "totalScore": 45,
              "finalScore": 50,
              "hasQuizzes": true
            }
          },
          {
            "id": 2,
            "name": "Church History",
            "code": "HIST201",
            "finalQuizzes": {
              "totalScore": 0,
              "finalScore": 0,
              "hasQuizzes": false
            },
            "weekQuizzes": {
              "totalScore": 30,
              "finalScore": 40,
              "hasQuizzes": true
            }
          }
        ]
      }
    ]
  }
}
```

#### Get Grades by Semester and Student
Returns detailed grades for a specific semester and student, including subcategory information for final quizzes.

**Endpoint:** `GET /grades/semester/:semesterId/student/:studentId`

**Example Request:**
```
GET /grades/semester/1/student/1
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Grades retrieved successfully",
  "data": {
    "semester": {
      "id": 1,
      "name": "Semester 1 2025"
    },
    "student": {
      "id": 1,
      "name": "Kirolos Younan",
      "code": "STU1"
    },
    "subjects": [
      {
        "id": 1,
        "name": "الحان",
        "code": "123",
        "quizzes": [
          {
            "name": "طقس رفع بخور عشية",
            "userGrade": 4,
            "finalGrade": 9,
            "subCategory": {
              "id": 1,
              "name": "حزاينى"
            }
          },
          {
            "name": "final category",
            "userGrade": 9,
            "finalGrade": 9,
            "subCategory": {
              "id": 2,
              "name": "Advanced Topics"
            }
          },
          {
            "name": "Week Quizzes",
            "userGrade": 18,
            "finalGrade": 28
          }
        ]
      },
      {
        "id": 2,
        "name": "arabic",
        "code": "ok",
        "quizzes": [
          {
            "name": "Week Quizzes",
            "userGrade": 8,
            "finalGrade": 19
          }
        ]
      }
    ]
  }
}
```

**Note:**
- Final quizzes include `subCategory` field with subcategory information (if assigned)
- Week quizzes are aggregated and don't include subcategory information
- `subCategory` will be `null` if no subcategory is assigned to the quiz

### Quiz Management

#### Get Quiz by ID
Returns detailed information about a specific quiz, including subcategory information.

**Endpoint:** `GET /quiz/:quizId`

**Example Request:**
```
GET /quiz/1
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Quiz retrieved successfully",
  "data": {
    "id": 1,
    "name": "Introduction to Theology Quiz",
    "subjectId": 1,
    "lessonId": 3,
    "grade": 100,
    "type": "Week",
    "numberOfAttempts": 3,
    "timeLimit": 1800,
    "isRecord": false,
    "subCategory": {
      "id": 1,
      "name": "Basic Concepts"
    },
    "subject": {
      "id": 1,
      "name": "Christian Theology",
      "code": "THEO101"
    },
    "lesson": {
      "id": 3,
      "name": "Introduction to Faith"
    },
    "content": [
      {
        "questionId": 0,
        "question": "What is theology?",
        "type": "text",
        "grade": 10
      }
    ],
    "createdAt": "2024-01-15T10:30:00.000Z",
    "updatedAt": "2024-01-15T10:30:00.000Z"
  }
}
```

#### Get Quizzes by Lesson ID
Returns quizzes associated with a specific lesson (replaces week-based quiz fetching).

**Endpoint:** `GET /quiz/lesson/:lessonId/simple`

**Example Request:**
```
GET /quiz/lesson/3/simple
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Quizzes retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Introduction Quiz",
      "grade": 10,
      "type": "Week",
      "timeLimit": 300,
      "numberOfAttempts": 3,
      "isRecord": false,
      "subject": {
        "id": 1,
        "name": "Christian Theology",
        "code": "THEO101"
      }
    }
  ]
}
```

### Submit Quiz Answers

The quiz answer submission format accepts an array of answers in JSON format:

**Example Request Body:**
```json
{
  "quizId": 1,
  "timeTaken": 300,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "selectedAnswerId": 1
    },
    {
      "type": "mcq",
      "questionId": 1,
      "selectedAnswerId": 1
    },
    {
      "type": "mcq",
      "questionId": 2,
      "selectedAnswerId": 0
    }
  ]
}
```

### Subject Subcategory Management

#### Add Subcategory to Subject
Adds a new subcategory to a subject.

**Endpoint:** `POST /subject/:subjectId/subcategory`

**Example Request:**
```
POST /subject/1/subcategory
Authorization: Bearer {jwt_token}

{
  "name": "Advanced Topics"
}
```

**Example Response:**
```json
{
  "message": "Subcategory added successfully",
  "data": {
    "id": 1,
    "name": "Christian Theology",
    "code": "THEO101",
    "subCategories": [
      {
        "id": 1,
        "name": "Advanced Topics"
      }
    ]
  }
}
```

#### Delete Subcategory from Subject
Removes a subcategory from a subject.

**Endpoint:** `DELETE /subject/:subjectId/subcategory/:subCategoryId`

**Example Request:**
```
DELETE /subject/1/subcategory/1
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Subcategory deleted successfully",
  "data": {
    "id": 1,
    "name": "Christian Theology",
    "code": "THEO101",
    "subCategories": []
  }
}
```

#### Get Subcategories by Subject ID
Returns all subcategories for a specific subject.

**Endpoint:** `GET /subject/:subjectId/subcategories`

**Example Request:**
```
GET /subject/1/subcategories
Authorization: Bearer {jwt_token}
```

**Example Response:**
```json
{
  "message": "Subcategories retrieved successfully",
  "data": {
    "subject": {
      "id": 1,
      "name": "Christian Theology",
      "code": "THEO101"
    },
    "subCategories": [
      {
        "id": 1,
        "name": "Advanced Topics"
      },
      {
        "id": 2,
        "name": "Basic Concepts"
      }
    ]
  }
}
```

### Create Subject with Subcategories

When creating a subject, you can now optionally include subcategories:

**Example Request:**
```json
POST /subject
Authorization: Bearer {jwt_token}

{
  "name": "Christian Theology",
  "code": "THEO101",
  "semesterTemplateId": 1,
  "teacherId": 2,
  "subCategories": [
    {
      "id": 1,
      "name": "Basic Concepts"
    },
    {
      "id": 2,
      "name": "Advanced Topics"
    }
  ]
}
```

### Create/Update Quiz with Subcategory

When creating or updating a quiz, you can now optionally include a single subcategory:

**Example Request:**
```json
POST /quiz
Authorization: Bearer {jwt_token}

{
  "name": "Theology Quiz 1",
  "subjectId": 1,
  "lessonId": 3,
  "grade": 100,
  "type": "Week",
  "numberOfAttempts": 3,
  "timeLimit": 1800,
  "subCategory": {
    "id": 1,
    "name": "Basic Concepts"
  },
  "content": [
    {
      "question": "What is theology?",
      "type": "text",
      "grade": 10
    }
  ]
}
```

## License

Nest is [MIT licensed](https://github.com/nestjs/nest/blob/master/LICENSE).
