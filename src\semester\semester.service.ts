import { Injectable, NotFoundException, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateSemesterTemplateDto } from './dto/semester-template.dto';
import { CreateSemesterDto } from './dto/create-semester.dto';

@Injectable()
export class SemesterService {
  private readonly logger = new Logger(SemesterService.name);

  constructor(private prisma: PrismaService) {}

  async createSemesterTemplate(createSemesterTemplateDto: CreateSemesterTemplateDto) {
    try {
      const newTemplate = await this.prisma.semesterTemplate.create({
        data: {
          semesterNo: createSemesterTemplateDto.semesterNo,
          name: createSemesterTemplateDto.name,
        },
      });

      return {
        message: 'Semester template created successfully',
        data: newTemplate,
      };
    } catch (error) {
      throw new Error(`Failed to create semester template: ${error.message}`);
    }
  }

  async getSemesterTemplateById(id: number) {
    try {
      if (!id) {
        throw new HttpException('Template ID is required', HttpStatus.BAD_REQUEST);
      }

      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id },
        include: {
          Subjects: true,
          Semesters: true,
        },
      });

      if (!template) {
        throw new HttpException(
          `Semester template with ID ${id} not found`,
          HttpStatus.NOT_FOUND
        );
      }

      // Parse subcategories for each subject
      const templateWithParsedSubCategories = {
        ...template,
        Subjects: template.Subjects.map(subject => ({
          ...subject,
          subCategories: subject.subCategories
            ? JSON.parse(subject.subCategories as string)
            : []
        }))
      };

      return {
        message: 'Semester template retrieved successfully',
        data: templateWithParsedSubCategories,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch semester template: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getAllSemesterTemplates() {
    try {
      const templates = await this.prisma.semesterTemplate.findMany({
        include: {
          Subjects: true,
          Semesters: true,
        },
      });

      // Parse subcategories for each subject in each template
      const templatesWithParsedSubCategories = templates.map(template => ({
        ...template,
        Subjects: template.Subjects.map(subject => ({
          ...subject,
          subCategories: subject.subCategories
            ? JSON.parse(subject.subCategories as string)
            : []
        }))
      }));

      return templatesWithParsedSubCategories;
    } catch (error) {
      throw new Error(`Failed to fetch semester templates: ${error.message}`);
    }
  }

  async updateSemesterTemplate(
    id: number,
    updateData: Partial<CreateSemesterTemplateDto>,
  ) {
    try {
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id },
      });

      if (!template) {
        throw new NotFoundException(`Semester template with ID ${id} not found`);
      }

      const updatedTemplate = await this.prisma.semesterTemplate.update({
        where: { id },
        data: updateData,
      });

      return {
        message: 'Semester template updated successfully',
        data: updatedTemplate,
      };
    } catch (error) {
      throw new Error(`Failed to update semester template: ${error.message}`);
    }
  }

  async deleteSemesterTemplate(id: number) {
    try {
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id },
      });

      if (!template) {
        throw new NotFoundException(`Semester template with ID ${id} not found`);
      }

      await this.prisma.semesterTemplate.delete({
        where: { id },
      });

      return {
        message: 'Semester template deleted successfully',
      };
    } catch (error) {
      throw new Error(`Failed to delete semester template: ${error.message}`);
    }
  }

  async addUserToSemester(userId: number, semesterId: number, role: 'student' | 'teacher') {
    try {
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId },
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      let user: any;

      if (role === 'student') {
        user = await this.prisma.student.findUnique({
          where: { userId },
          include: {
            semester: true
          }
        });

        if (!user) {
          throw new NotFoundException(`Student with userId ${userId} not found`);
        }

        // Connect the student to the semester (many-to-many relationship)
        await this.prisma.student.update({
          where: { userId },
          data: {
            semester: {
              connect: { id: semesterId }
            }
          },
          include: {
            semester: true
          }
        });

      } else if (role === 'teacher') {
        user = await this.prisma.teacher.findUnique({
          where: { userId },
          include: {
            semester: true
          }
        });

        if (!user) {
          throw new NotFoundException(`Teacher with userId ${userId} not found`);
        }

        // Connect the teacher to the semester (many-to-many relationship)
        await this.prisma.teacher.update({
          where: { userId },
          data: {
            semester: {
              connect: { id: semesterId }
            }
          },
          include: {
            semester: true
          }
        });
      }

      return {
        message: `${role.charAt(0).toUpperCase() + role.slice(1)} added to semester successfully`,
        data: {
          user,
          semester
        }
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to add user to semester: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async addSubjectToTemplate(subjectData: {
    templateId: number;
    subjectId?: number;
    name?: string;
    code?: string;
    teacherId?: number | null;
  }) {
    const { templateId, subjectId, name, code, teacherId } = subjectData;

    try {
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id: templateId },
      });

      if (!template) {
        throw new NotFoundException(`Semester template with ID ${templateId} not found`);
      }

      let subject;
      if (subjectId) {
        // If updating existing subject
        subject = await this.prisma.subject.update({
          where: { id: subjectId },
          data: { semesterTemplateId: templateId },
        });
      } else {
        // If creating new subject
        subject = await this.prisma.subject.create({
          data: {
            name,
            code,
            teacherId: teacherId || null,
            semesterTemplateId: templateId,
          },
        });
      }

      return {
        message: 'Subject added to semester template successfully',
        data: subject,
      };
    } catch (error) {
      throw new Error(`Failed to add subject to semester template: ${error.message}`);
    }
  }

  async getWeeksBySemester(semesterId: number) {
    try {
      const weeks = await this.prisma.week.findMany({
        where: { semesterId },
        include: {
          lessons: {
            include: {
              lesson: true
            },
            orderBy: {
              order: 'asc'
            }
          }
        },
        orderBy: {
          weekNo: 'asc'
        }
      });

      if (!weeks.length) {
        throw new NotFoundException(`No weeks found for semester with ID ${semesterId}`);
      }

      return {
        message: 'Weeks retrieved successfully',
        data: weeks
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch weeks: ${error.message}`);
    }
  }

  async addWeekToSemester(
    semesterId: number,
    { weekNo, startDate, endDate }: { weekNo: number; startDate: Date; endDate: Date }
  ) {
    try {
      // Validate input
      if (!weekNo || !startDate || !endDate) {
        throw new HttpException(
          'weekNo, startDate, and endDate are required',
          HttpStatus.BAD_REQUEST
        );
      }

      // Check if the semester exists
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId }
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      // Create and associate the week with the semester
      const newWeek = await this.prisma.week.create({
        data: {
          weekNo,
          semesterId,
          startDate,
          endDate,
        },
      });

      return {
        message: 'Week successfully added to semester',
        data: newWeek,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to add week to semester: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async addLessonToWeek(weekId: number, { lessonId }: { lessonId: number }) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({ where: { id: weekId } });
      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Check if lesson exists
      const lesson = await this.prisma.lesson.findUnique({ where: { id: lessonId } });
      if (!lesson) {
        throw new NotFoundException(`Lesson with ID ${lessonId} not found`);
      }

      // Get the current highest order for this week
      const highestOrder = await this.prisma.weekLesson.findFirst({
        where: { weekId },
        orderBy: { order: 'desc' },
        select: { order: true },
      });

      // Set the new order to be one more than the current highest (or 1 if no lessons exist)
      const newOrder = (highestOrder?.order ?? 0) + 1;

      // Create the week-lesson association
      const weekLesson = await this.prisma.weekLesson.create({
        data: {
          weekId,
          lessonId,
          order: newOrder,
        },
        include: {
          lesson: true,
          week: true,
        },
      });

      return {
        message: 'Lesson successfully added to week',
        data: weekLesson,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      if (error.code === 'P2002') {
        throw new Error('This lesson is already assigned to this week');
      }
      throw new Error(`Failed to add lesson to week: ${error.message}`);
    }
  }

  async removeLessonFromWeek(weekId: number, lessonId: number) {
    try {
      // Check if the association exists
      const weekLesson = await this.prisma.weekLesson.findFirst({
        where: {
          weekId,
          lessonId,
        },
      });

      if (!weekLesson) {
        throw new NotFoundException(`Lesson ${lessonId} not found in week ${weekId}`);
      }

      // Delete the association
      await this.prisma.weekLesson.delete({
        where: {
          id: weekLesson.id,
        },
      });

      return {
        message: 'Lesson successfully removed from week',
        data: null,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to remove lesson from week: ${error.message}`);
    }
  }

  async createSemester(createSemesterDto: CreateSemesterDto) {
    try {
      // Check if semester template exists
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id: createSemesterDto.semesterTemplateId },
      });

      if (!template) {
        throw new NotFoundException(
          `Semester template with ID ${createSemesterDto.semesterTemplateId} not found`,
        );
      }

      // If isCurrent is true, set all other semesters' isCurrent to false
      if (createSemesterDto.isCurrent) {
        await this.prisma.semester.updateMany({
          where: { isCurrent: true },
          data: { isCurrent: false },
        });
      }

      const semester = await this.prisma.semester.create({
        data: {
          semesterTemplateId: createSemesterDto.semesterTemplateId,
          year: createSemesterDto.year,
          name: createSemesterDto.name,
          startDate: createSemesterDto.startDate,
          endDate: createSemesterDto.endDate,
          isCurrent: createSemesterDto.isCurrent ?? false,
        },
        include: {
          semesterTemplate: {
            include: {
              Subjects: true,
            }
          },
          students: true,
          Teachers: true,
          Weeks: true,
        },
      });

      // Parse subcategories for each subject
      const semesterWithParsedSubCategories = {
        ...semester,
        semesterTemplate: {
          ...semester.semesterTemplate,
          Subjects: semester.semesterTemplate.Subjects.map(subject => ({
            ...subject,
            subCategories: subject.subCategories
              ? JSON.parse(subject.subCategories as string)
              : []
          }))
        }
      };

      return {
        message: 'Semester created successfully',
        data: semesterWithParsedSubCategories,
      };
    } catch (error) {
      this.logger.error(`Failed to create semester: ${error.message}`);
      throw new HttpException(
        `Failed to create semester: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async assignTemplateToSemester(semesterId: number, semesterTemplateId: number) {
    try {
      // Check if semester exists
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId },
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      // Check if semester template exists
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id: semesterTemplateId },
      });

      if (!template) {
        throw new NotFoundException(
          `Semester template with ID ${semesterTemplateId} not found`,
        );
      }

      // Update the semester with the new template
      const updatedSemester = await this.prisma.semester.update({
        where: { id: semesterId },
        data: { semesterTemplateId },
        include: {
          semesterTemplate: {
            include: {
              Subjects: true,
            },
          },
          students: true,
          Teachers: true,
          Weeks: true,
        },
      });

      // Parse subcategories for each subject
      const semesterWithParsedSubCategories = {
        ...updatedSemester,
        semesterTemplate: {
          ...updatedSemester.semesterTemplate,
          Subjects: updatedSemester.semesterTemplate.Subjects.map(subject => ({
            ...subject,
            subCategories: subject.subCategories
              ? JSON.parse(subject.subCategories as string)
              : []
          }))
        }
      };

      return {
        message: 'Semester template assigned successfully',
        data: semesterWithParsedSubCategories,
      };
    } catch (error) {
      throw new Error(`Failed to assign semester template: ${error.message}`);
    }
  }

  async getAllSemesters() {
    try {
      const semesters = await this.prisma.semester.findMany({
        include: {
          semesterTemplate: {
            include: {
              Subjects: true,
            },
          },
          students: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true,
                  birthday: true,
                  nationality: true,
                  Address: true,
                  gender: true,
                  createdAt: true,
                  updatedAt: true,
                }
              }
            }
          },
          Teachers: {
            include: {
              user: {
                select: {
                  id: true,
                  email: true,
                  name: true,
                  birthday: true,
                  nationality: true,
                  Address: true,
                  gender: true,
                  createdAt: true,
                  updatedAt: true,
                }
              }
            }
          },
          Weeks: true,
        },
        orderBy: {
          startDate: 'desc',
        },
      });

      // Parse subcategories for each subject in each semester
      const semestersWithParsedSubCategories = semesters.map(semester => ({
        ...semester,
        semesterTemplate: {
          ...semester.semesterTemplate,
          Subjects: semester.semesterTemplate.Subjects.map(subject => ({
            ...subject,
            subCategories: subject.subCategories
              ? JSON.parse(subject.subCategories as string)
              : []
          }))
        }
      }));

      return {
        message: 'Semesters retrieved successfully',
        data: semestersWithParsedSubCategories,
      };
    } catch (error) {
      throw new Error(`Failed to fetch semesters: ${error.message}`);
    }
  }

  async getSemesterById(id: number) {
    try {
      if (!id) {
        throw new HttpException('Semester ID is required', HttpStatus.BAD_REQUEST);
      }

      const semester = await this.prisma.semester.findUnique({
        where: { id },
        include: {
          semesterTemplate: {
            include: {
              Subjects: true,
            },
          },
          students: true,
          Teachers: true,
          Weeks: true,
        },
      });

      if (!semester) {
        throw new HttpException(
          `Semester with ID ${id} not found`,
          HttpStatus.NOT_FOUND
        );
      }

      // Parse subcategories for each subject
      const semesterWithParsedSubCategories = {
        ...semester,
        semesterTemplate: {
          ...semester.semesterTemplate,
          Subjects: semester.semesterTemplate.Subjects.map(subject => ({
            ...subject,
            subCategories: subject.subCategories
              ? JSON.parse(subject.subCategories as string)
              : []
          }))
        }
      };

      return {
        message: 'Semester retrieved successfully',
        data: semesterWithParsedSubCategories,
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch semester: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getCurrentSemester() {
    try {
      const currentSemester = await this.prisma.semester.findFirst({
        where: {
          isCurrent: true,
        },
        include: {
          semesterTemplate: {
            include: {
              Subjects: true,
            },
          },
          students: true,
          Teachers: true,
          Weeks: true,
        },
      });

      if (!currentSemester) {
        throw new NotFoundException('No current semester found');
      }

      // Parse subcategories for each subject
      const currentSemesterWithParsedSubCategories = {
        ...currentSemester,
        semesterTemplate: {
          ...currentSemester.semesterTemplate,
          Subjects: currentSemester.semesterTemplate.Subjects.map(subject => ({
            ...subject,
            subCategories: subject.subCategories
              ? JSON.parse(subject.subCategories as string)
              : []
          }))
        }
      };

      return {
        message: 'Current semester retrieved successfully',
        data: currentSemesterWithParsedSubCategories,
      };
    } catch (error) {
      throw new Error(`Failed to fetch current semester: ${error.message}`);
    }
  }

  async getEnrolledSemesters(userId: number): Promise<Array<{ id: number; semesterNo: number }>> {
    try {
      const student = await this.prisma.student.findUnique({
        where: { userId },
        include: {
          semester: {
            include: {
              semesterTemplate: {
                select: {
                  semesterNo: true
                }
              }
            }
          }
        }
      });

      if (!student) {
        throw new HttpException(
          `Student with userId ${userId} not found`,
          HttpStatus.NOT_FOUND
        );
      }

      return student.semester.map(sem => ({
        id: sem.id,
        semesterNo: sem.semesterTemplate.semesterNo
      }));
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch enrolled semesters: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getLessonsByWeekId(weekId: number) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId },
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Get lessons for the week
      const weekLessons = await this.prisma.weekLesson.findMany({
        where: { weekId },
        include: {
          lesson: {
            include: {
              subject: true,
              Items: true
            }
          }
        },
        orderBy: {
          order: 'asc'
        }
      });

      // Parse subcategories for subjects in lessons
      const weekLessonsWithParsedSubCategories = weekLessons.map(weekLesson => ({
        ...weekLesson,
        lesson: {
          ...weekLesson.lesson,
          subject: {
            ...weekLesson.lesson.subject,
            subCategories: weekLesson.lesson.subject.subCategories
              ? JSON.parse(weekLesson.lesson.subject.subCategories as string)
              : []
          }
        }
      }));

      return {
        message: 'Lessons retrieved successfully',
        data: weekLessonsWithParsedSubCategories
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch lessons: ${error.message}`);
    }
  }

  async getStudentsBySemesterId(semesterId: number) {
    try {
      // Check if the semester exists
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId },
        select: { id: true, name: true },
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      // Get all students enrolled in this semester
      const students = await this.prisma.student.findMany({
        where: {
          semester: {
            some: {
              id: semesterId,
            },
          },
        },
        select: {
          id: true,
          studentCode: true,
          user: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          studentCode: 'asc',
        },
      });

      return {
        message: 'Students retrieved successfully',
        data: {
          semester: {
            id: semester.id,
            name: semester.name,
          },
          students: students.map(student => ({
            id: student.id,
            name: student.user.name,
            code: student.studentCode,
          })),
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get students for semester ${semesterId}: ${error.message}`);
      throw error;
    }
  }

  async getSubjectsBySemesterId(semesterId: number) {
    try {
      // Check if semester exists and get its template
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId },
        include: {
          semesterTemplate: {
            include: {
              Subjects: {
                include: {
                  teacher: {
                    include: {
                      user: {
                        select: {
                          id: true,
                          name: true,
                          email: true
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      if (!semester.semesterTemplate) {
        throw new NotFoundException(`No template found for semester with ID ${semesterId}`);
      }

      // Parse subcategories for each subject
      const subjectsWithParsedSubCategories = semester.semesterTemplate.Subjects.map(subject => ({
        ...subject,
        subCategories: subject.subCategories
          ? JSON.parse(subject.subCategories as string)
          : []
      }));

      return {
        message: 'Subjects retrieved successfully',
        data: subjectsWithParsedSubCategories
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch subjects: ${error.message}`);
    }
  }

  async updateWeek(
    weekId: number,
    updateData: { weekNo?: number; startDate?: Date; endDate?: Date }
  ) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId }
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Update the week
      const updatedWeek = await this.prisma.week.update({
        where: { id: weekId },
        data: updateData,
        include: {
          lessons: {
            include: {
              lesson: true
            },
            orderBy: {
              order: 'asc'
            }
          }
        }
      });

      return {
        message: 'Week updated successfully',
        data: updatedWeek
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to update week: ${error.message}`);}
    }
  async getSemestersWithSubjects() {
    try {
      const semesters = await this.prisma.semester.findMany({
        select: {
          id: true,
          name: true,
          semesterTemplate: {
            select: {
              Subjects: {
                select: {
                  id: true,
                  name: true
                }
              }
            }
          }
        },
        orderBy: {
          startDate: 'desc'
        }
      });

      // Flatten the response structure
      const formattedSemesters = semesters.map(semester => ({
        id: semester.id,
        name: semester.name,
        subjects: semester.semesterTemplate?.Subjects || []
      }));

      return {
        message: 'Semesters list retrieved successfully',
        data: formattedSemesters
      };
    } catch (error) {
      this.logger.error(`Failed to fetch semesters list: ${error.message}`);
      throw new HttpException(
        'Failed to fetch semesters list',
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async removeUserFromSemester(userId: number, semesterId: number, role: 'student' | 'teacher') {
    try {
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId },
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      if (role === 'student') {
        const student = await this.prisma.student.findUnique({
          where: { userId },
          include: {
            semester: true
          }
        });

        if (!student) {
          throw new NotFoundException(`Student with userId ${userId} not found`);
        }

        // Disconnect the student from the semester
        await this.prisma.student.update({
          where: { userId },
          data: {
            semester: {
              disconnect: { id: semesterId }
            }
          }
        });

      } else if (role === 'teacher') {
        const teacher = await this.prisma.teacher.findUnique({
          where: { userId },
          include: {
            semester: true
          }
        });

        if (!teacher) {
          throw new NotFoundException(`Teacher with userId ${userId} not found`);
        }

        // Disconnect the teacher from the semester
        await this.prisma.teacher.update({
          where: { userId },
          data: {
            semester: {
              disconnect: { id: semesterId }
            }
          }
        });
      }

      return {
        message: `${role.charAt(0).toUpperCase() + role.slice(1)} removed from semester successfully`,
        data: null
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to remove user from semester: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getSubjectsAndLessonsByWeekId(weekId: number, studentId?: number) {
    try {
      console.log(`Getting subjects and lessons for week ID: ${weekId}, student ID: ${studentId || 'none'}`);
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId },
        include: {
          semester: {
            include: {
              semesterTemplate: {
                include: {
                  Subjects: {
                    include: {
                      Lessons: {
                        include: {
                          Items: true,
                          weeks: {
                            where: {
                              weekId
                            }
                          }
                        }
                      }
                    }
                  }
                }
              }
            }
          }
        }
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Get and transform week lessons data
      const weekLessons = await this.prisma.weekLesson.findMany({
        where: { weekId },
        include: {
          lesson: {
            include: {
              subject: true,
              Items: true
            }
          }
        },
        orderBy: {
          order: 'asc'
        }
      });

      // Get all lesson IDs for this week
      const lessonIds = weekLessons.map(wl => wl.lesson.id);
      console.log(`Found ${lessonIds.length} lessons for week ID: ${weekId}`);
      console.log(`Lesson IDs: ${lessonIds.join(', ')}`);

      // Get all quizzes for these lessons
      const quizzes = await this.prisma.quiz.findMany({
        where: {
          lessonId: {
            in: lessonIds.length > 0 ? lessonIds : [-1] // Use -1 as a placeholder if no lessons found
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      console.log(`Found ${quizzes.length} quizzes for lessons in week ID: ${weekId}`);
      quizzes.forEach((quiz, index) => {
        console.log(`Quiz ${index + 1}: ID=${quiz.id}, Name=${quiz.name}, LessonID=${quiz.lessonId || 'null'}, Type=${quiz.type}`);
      });

      // Define the quiz type
      interface QuizWithAttempts {
        id: number;
        name: string;
        timeLimit: number;
        grade: number;
        numberOfAttempts: number;
        type: string;
        isRecord?: boolean;
        lessonId: number | null;
        order: number;
        userAttempts: number;
        status: string;
      }

      // Create a map of lesson ID to quizzes
      const lessonQuizzesMap: Record<number, QuizWithAttempts[]> = {};

      // Initialize the map with empty arrays for each lesson
      lessonIds.forEach(id => {
        lessonQuizzesMap[id] = [];
        console.log(`Initialized empty quizzes array for lesson ID: ${id}`);
      });

      // Process each quiz
      quizzes.forEach((quiz, index) => {
        const quizWithAttempts: QuizWithAttempts = {
          id: quiz.id,
          name: quiz.name,
          timeLimit: quiz.timeLimit,
          grade: quiz.grade,
          numberOfAttempts: quiz.numberOfAttempts,
          type: quiz.type,
          lessonId: quiz.lessonId,
          isRecord: quiz.isRecord,
          order: index, // Use index as order since we don't have WeekQuiz anymore
          userAttempts: 0, // Default value, will be updated if studentId is provided
          status: 'open' // Default value, will be updated if studentId is provided
        };

        // If the quiz has a lessonId and that lesson is part of this week, add it to that lesson
        if (quiz.lessonId && lessonIds.includes(quiz.lessonId)) {
          lessonQuizzesMap[quiz.lessonId].push(quizWithAttempts);
          console.log(`Assigned quiz ID ${quiz.id} to its associated lesson ID ${quiz.lessonId}`);
        } else {
          // If the quiz doesn't have a lessonId or the lesson is not part of this week,
          // add it to the first lesson as a fallback
          if (lessonIds.length > 0) {
            lessonQuizzesMap[lessonIds[0]].push(quizWithAttempts);
            console.log(`Assigned quiz ID ${quiz.id} to the first lesson ID ${lessonIds[0]} as fallback`);
          } else {
            console.log(`Could not assign quiz ID ${quiz.id} - no lessons available`);
          }
        }
      });

      // Get all quiz IDs
      const allQuizIds = quizzes.map(quiz => quiz.id);

      // If studentId is provided, get quiz attempts and determine status
      const quizAttemptsMap = {};
      const quizStatusMap = {};

      if (studentId && allQuizIds.length > 0) {
        // Get all quiz answers for this student for all quizzes
        const quizAnswers = await this.prisma.quizAnswer.findMany({
          where: {
            studentId,
            quizId: {
              in: allQuizIds
            }
          },
          orderBy: {
            createdAt: 'desc'
          },
          include: {
            quiz: {
              select: {
                id: true,
                grade: true,
                numberOfAttempts: true
              }
            }
          }
        });

        // Group quiz answers by quiz ID
        const quizAnswersByQuiz = quizAnswers.reduce((acc, qa) => {
          if (!acc[qa.quizId]) {
            acc[qa.quizId] = [];
          }
          acc[qa.quizId].push(qa);
          return acc;
        }, {});

        // Count attempts and determine status for each quiz
        Object.keys(quizAnswersByQuiz).forEach(quizId => {
          const answers = quizAnswersByQuiz[quizId];
          quizAttemptsMap[quizId] = answers.length;

          // Get the latest attempt
          const latestAttempt = answers[0]; // Already ordered by createdAt desc

          // Determine status based on the latest attempt
          if (latestAttempt.grade === null) {
            quizStatusMap[quizId] = 'pending';
          } else {
            const finalGrade = latestAttempt.finalGrade;
            const grade = latestAttempt.grade;

            if (grade === finalGrade) {
              quizStatusMap[quizId] = 'excellent';
            } else if (grade >= finalGrade * 0.5) {
              quizStatusMap[quizId] = 'pass';
            } else {
              quizStatusMap[quizId] = 'fail';
            }
          }
        });

        // For quizzes with no attempts, check if the week is closed
        const currentDate = new Date();
        if (week.endDate < currentDate) {
          allQuizIds.forEach(quizId => {
            if (!quizAttemptsMap[quizId]) {
              quizStatusMap[quizId] = 'closed';
              quizAttemptsMap[quizId] = 0;
            }
          });
        }

        // Check for quizzes that need to be checked in the Redo table
        const quizzesToCheck = [];

        // Collect quizzes that are either closed or failed with max attempts reached
        allQuizIds.forEach(quizId => {
          const numId = Number(quizId);
          const status = quizStatusMap[quizId];
          const attempts = quizAttemptsMap[quizId] || 0;

          // Find the quiz to get numberOfAttempts
          const quiz = quizzes.find(q => q.id === numId);

          if (status === 'closed' || (status === 'fail' && attempts >= quiz?.numberOfAttempts)) {
            quizzesToCheck.push(numId);
          }
        });

        // If there are quizzes to check, query the Redo table
        if (quizzesToCheck.length > 0) {
          const redoEntries = await this.prisma.redo.findMany({
            where: {
              quizId: {
                in: quizzesToCheck
              },
              studentId: studentId
            }
          });

          // Update status to 'redo' for quizzes that have a redo entry
          redoEntries.forEach(redo => {
            quizStatusMap[redo.quizId] = 'redo';
          });
        }
      }



      // Group lessons by subject and include quizzes with each lesson
      const subjects = Object.values(weekLessons.reduce((acc: { [key: string]: any }, weekLesson) => {
        const subject = weekLesson.lesson.subject;
        if (!acc[subject.id]) {
          acc[subject.id] = {
            id: subject.id,
            name: subject.name,
            code: subject.code,
            lessons: []
          };
        }

        // Get quizzes for this lesson and add attempt counts and status
        const lessonId = weekLesson.lesson.id;
        const quizzes = (lessonQuizzesMap[lessonId] || []).map(quiz => ({
          ...quiz,
          userAttempts: quizAttemptsMap[quiz.id] || 0,
          status: quizStatusMap[quiz.id] || 'open' // Default to 'open' if no status is set
        }));

        acc[subject.id].lessons.push({
          id: lessonId,
          name: weekLesson.lesson.name,
          items: weekLesson.lesson.Items,
          quizzes: quizzes,
          order: weekLesson.order
        });

        return acc;
      }, {}));

      // Log the final quizzes map
      console.log('Final quizzes map:');
      Object.keys(lessonQuizzesMap).forEach(lessonId => {
        console.log(`Lesson ID ${lessonId}: ${lessonQuizzesMap[Number(lessonId)].length} quizzes`);
      });

      // Log the final subjects structure
      console.log('Final subjects structure:');
      subjects.forEach((subject: any) => {
        console.log(`Subject ID ${subject.id}: ${subject.name}`);
        subject.lessons.forEach((lesson: any) => {
          console.log(`  Lesson ID ${lesson.id}: ${lesson.name}, ${lesson.quizzes.length} quizzes`);
          lesson.quizzes.forEach((quiz: any) => {
            console.log(`    Quiz ID ${quiz.id}: ${quiz.name}, Status: ${quiz.status}, Attempts: ${quiz.userAttempts}`);
          });
        });
      });

      return {
        message: 'Subjects, lessons and quizzes retrieved successfully',
        data: {
          subjects
        }
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch subjects, lessons and quizzes: ${error.message}`);
    }
  }

  async getSemesterWeeksList(semesterId: number): Promise<Array<{
    id: number;
    weekNo: number;
    startDate: Date;
    endDate: Date;
  }>> {
    try {
      const weeks = await this.prisma.week.findMany({
        where: { semesterId },
        select: {
          id: true,
          weekNo: true,
          startDate: true,
          endDate: true
        },
        orderBy: {
          weekNo: 'asc'
        }
      });

      if (!weeks.length) {
        throw new NotFoundException(`No weeks found for semester with ID ${semesterId}`);
      }

      return weeks;
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch weeks: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
