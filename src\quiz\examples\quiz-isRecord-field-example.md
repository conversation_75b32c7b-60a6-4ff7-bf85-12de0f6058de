# Quiz isRecord Field API Examples

## Overview

The `isRecord` field has been added to the quiz model to indicate whether a quiz supports recording functionality. This optional boolean field defaults to `false` and can be used to enable special recording features for specific quizzes.

## Create Quiz with isRecord

### Endpoint

```
POST /quiz
```

### Example Request (with isRecord = true)

```json
{
  "name": "Oral Examination - Music Performance",
  "subjectId": 1,
  "lessonId": 2,
  "grade": 10,
  "type": "Final",
  "numberOfAttempts": 1,
  "timeLimit": 30,
  "isRecord": true,
  "content": [
    {
      "type": "record",
      "question": "Record yourself singing the hymn 'Amazing Grace'",
      "grade": 5,
      "maxDuration": 300
    },
    {
      "type": "text",
      "question": "Explain the historical significance of this hymn",
      "grade": 3,
      "correctAnswer": "Written by <PERSON> in 1772..."
    },
    {
      "type": "mcq",
      "question": "What key is this hymn traditionally sung in?",
      "grade": 2,
      "answers": [
        {"id": 0, "text": "G Major"},
        {"id": 1, "text": "C Major"},
        {"id": 2, "text": "D Major"},
        {"id": 3, "text": "F Major"}
      ],
      "correctAnswerId": 0
    }
  ]
}
```

### Example Request (without isRecord - defaults to false)

```json
{
  "name": "Music Theory Quiz",
  "subjectId": 1,
  "lessonId": 2,
  "grade": 10,
  "type": "Week",
  "numberOfAttempts": 2,
  "timeLimit": 20,
  "content": [
    {
      "type": "mcq",
      "question": "What is a major scale?",
      "grade": 5,
      "answers": [
        {"id": 0, "text": "A scale with 7 notes"},
        {"id": 1, "text": "A scale with sharps and flats"},
        {"id": 2, "text": "A scale following the pattern W-W-H-W-W-W-H"},
        {"id": 3, "text": "A scale in a high pitch"}
      ],
      "correctAnswerId": 2
    },
    {
      "type": "text",
      "question": "Name three composers from the Baroque period",
      "grade": 5,
      "correctAnswer": "Bach, Handel, Vivaldi"
    }
  ]
}
```

## Update Quiz with isRecord

### Endpoint

```
PUT /quiz/:id
```

### Example Request

```json
{
  "name": "Updated Oral Examination",
  "isRecord": false,
  "timeLimit": 45
}
```

## Get Quiz Responses with isRecord

### Get Quiz by ID

```
GET /quiz/3
```

### Example Response

```json
{
  "message": "Quiz retrieved successfully",
  "data": {
    "id": 3,
    "name": "Oral Examination - Music Performance",
    "subjectId": 1,
    "grade": 10,
    "createdAt": "2025-05-22T19:45:38.476Z",
    "updatedAt": "2025-05-22T19:45:38.476Z",
    "type": "Final",
    "numberOfAttempts": 1,
    "timeLimit": 30,
    "lessonId": 2,
    "isRecord": true,
    "subject": {
      "id": 1,
      "name": "الحان",
      "code": "MUS101"
    },
    "lesson": {
      "id": 2,
      "name": "Advanced Hymns"
    },
    "content": [
      {
        "questionId": 0,
        "type": "record",
        "question": "Record yourself singing the hymn 'Amazing Grace'",
        "grade": 5,
        "maxDuration": 300
      },
      {
        "questionId": 1,
        "type": "text",
        "question": "Explain the historical significance of this hymn",
        "grade": 3,
        "correctAnswer": "Written by John Newton in 1772..."
      },
      {
        "questionId": 2,
        "type": "mcq",
        "question": "What key is this hymn traditionally sung in?",
        "grade": 2,
        "answers": [
          {"id": 0, "text": "G Major"},
          {"id": 1, "text": "C Major"},
          {"id": 2, "text": "D Major"},
          {"id": 3, "text": "F Major"}
        ],
        "correctAnswerId": 0
      }
    ]
  }
}
```

### Get Simple Quizzes by Week

```
GET /quiz/week/2/simple
```

### Example Response

```json
{
  "message": "Quizzes retrieved successfully",
  "data": [
    {
      "id": 1,
      "name": "Music Theory Quiz",
      "type": "Week",
      "grade": 10,
      "timeLimit": 20,
      "numberOfAttempts": 2,
      "isRecord": false,
      "subjectName": "الحان",
      "order": 1
    },
    {
      "id": 3,
      "name": "Oral Examination - Music Performance",
      "type": "Final",
      "grade": 10,
      "timeLimit": 30,
      "numberOfAttempts": 1,
      "isRecord": true,
      "subjectName": "الحان",
      "order": 2
    }
  ]
}
```

## Use Cases

### Recording-Enabled Quizzes (isRecord: true)
- **Oral examinations**: Students record spoken answers
- **Music performance**: Students record themselves playing instruments or singing
- **Language practice**: Students record pronunciation exercises
- **Presentation skills**: Students record presentations or speeches

### Standard Quizzes (isRecord: false or not specified)
- **Written examinations**: Traditional text and multiple-choice questions
- **Theory tests**: Knowledge-based assessments
- **Quick assessments**: Short quizzes without recording requirements

## Frontend Implementation Notes

1. **Conditional UI**: Show recording interface only when `isRecord: true`
2. **Question Types**: Support `record` type questions in recording-enabled quizzes
3. **File Upload**: Implement audio/video upload functionality for record questions
4. **Permissions**: Request microphone/camera permissions for recording quizzes
5. **Validation**: Ensure recording questions are only used in `isRecord: true` quizzes

## Database Schema

```sql
-- The isRecord field in the quiz table
isRecord BOOLEAN DEFAULT false
```

The field is optional and defaults to `false`, ensuring backward compatibility with existing quizzes.
