import { Injectable, NotFoundException, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class UserService {
  constructor(private prisma: PrismaService) {}

  async getAllStudents(isVerified?: boolean) {
    try {
      const whereClause = isVerified !== undefined ? { isVerified } : {};

      const students = await this.prisma.student.findMany({
        where: whereClause,
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              birthday: true,
              nationality: true,
              Address: true,
              gender: true,
              phone: true,
              profilePicture: true,
              createdAt: true,
              updatedAt: true,
              // Excluding password and hashedRt for security
            }
          },
          semester: true,
          CompletedSemesters: true,
        }
      });

      return {
        message: 'Students retrieved successfully',
        data: students,
      };
    } catch (error) {
      throw new Error(`Failed to fetch students: ${error.message}`);
    }
  }

  async getAllTeachers() {
    try {
      const teachers = await this.prisma.teacher.findMany({
        include: {
          user: {
            select: {
              id: true,
              email: true,
              name: true,
              profilePicture: true,
              createdAt: true,
              updatedAt: true,
              // Excluding password and hashedRt for security
            }
          },
          Subjects: true,
          semester: true,
        }
      });

      return {
        message: 'Teachers retrieved successfully',
        data: teachers,
      };
    } catch (error) {
      throw new Error(`Failed to fetch teachers: ${error.message}`);
    }
  }

  async verifyStudent(studentId: number, action: 'approve' | 'decline') {
    try {
      const student = await this.prisma.student.findUnique({
        where: { id: studentId },
        include: { user: true }
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      if (action === 'approve') {
        // Update isVerified to true
        const updatedStudent = await this.prisma.student.update({
          where: { id: studentId },
          data: { isVerified: true },
          include: {
            user: {
              select: {
                id: true,
                email: true,
                name: true,
                birthday: true,
                nationality: true,
                Address: true,
                gender: true,
                profilePicture: true,
                createdAt: true,
                updatedAt: true,
              }
            }
          }
        });

        return {
          message: 'Student approved successfully',
          data: updatedStudent
        };

      } else {
        // Delete both student and user records
        await this.prisma.$transaction([
          this.prisma.student.delete({
            where: { id: studentId }
          }),
          this.prisma.user.delete({
            where: { id: student.userId }
          })
        ]);

        return {
          message: 'Student declined and removed successfully',
          data: null
        };
      }
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to process student verification: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getAllStudentsSummary() {
    try {
      const students = await this.prisma.student.findMany({
        where: {
          isVerified: true
        },
        select: {
          id: true,
          userId: true,
          studentCode: true,
          user: {
            select: {
              name: true
            }
          }
        }
      });

      return {
        message: 'Students summary retrieved successfully',
        data: students
      };
    } catch (error) {
      throw new Error(`Failed to fetch students summary: ${error.message}`);
    }
  }

  async getStudentInfo(userId: number) {
    const student = await this.prisma.student.findUnique({
      where: { userId },
      include: {
        user: {
          select: {
            id: true,
            email: true,
            name: true,
            birthday: true,
            nationality: true,
            Address: true,
            gender: true,
            profilePicture: true,
            createdAt: true,
            updatedAt: true,
          }
        },
        semester: true,
        CompletedSemesters: true
      }
    });

    if (!student) {
      throw new NotFoundException(`Student with userId ${userId} not found`);
    }

    return student;
  }

  async updateProfilePicture(userId: number, profilePicture: string) {
    try {
      // Check if user exists
      const user = await this.prisma.user.findUnique({
        where: { id: userId }
      });

      if (!user) {
        throw new NotFoundException(`User with ID ${userId} not found`);
      }

      // Update the profile picture
      const updatedUser = await this.prisma.user.update({
        where: { id: userId },
        data: { profilePicture },
        select: {
          id: true,
          email: true,
          name: true,
          profilePicture: true,
          createdAt: true,
          updatedAt: true,
        }
      });

      return {
        message: 'Profile picture updated successfully',
        data: updatedUser
      };
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        `Failed to update profile picture: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
