# MCQ-Only Quiz Example

## Quiz from Database

```json
{
  "id": 2,
  "name": "test2",
  "subjectId": 1,
  "grade": 9,
  "createdAt": "2025-05-22T17:35:26.443Z",
  "updatedAt": "2025-05-22T17:35:26.443Z",
  "type": "Week",
  "numberOfAttempts": 1,
  "timeLimit": 30,
  "content": [
    {
      "question": "fd",
      "type": "mcq",
      "grade": 1,
      "answers": [
        {
          "id": 0,
          "text": "gfd"
        },
        {
          "id": 1,
          "text": "gf"
        }
      ],
      "correctAnswerId": 1
    },
    {
      "question": "fd",
      "type": "mcq",
      "grade": 3,
      "answers": [
        {
          "id": 0,
          "text": "gf"
        },
        {
          "id": 1,
          "text": "gfd"
        }
      ],
      "correctAnswerId": 0
    },
    {
      "question": "hgfd",
      "type": "mcq",
      "grade": 5,
      "answers": [
        {
          "id": 0,
          "text": "gf"
        },
        {
          "id": 1,
          "text": "hgf"
        }
      ],
      "correctAnswerId": 1
    }
  ],
  "lessonId": 1
}
```

## Request Example

```json
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 2,
  "timeTaken": 900,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "question": "fd",
      "questionGrade": 1,
      "selectedAnswerId": 1,
      "userAnswer": "gf"
    },
    {
      "type": "mcq",
      "questionId": 1,
      "question": "fd",
      "questionGrade": 3,
      "selectedAnswerId": 0,
      "userAnswer": "gf"
    },
    {
      "type": "mcq",
      "questionId": 2,
      "question": "hgfd",
      "questionGrade": 5,
      "selectedAnswerId": 1,
      "userAnswer": "hgf"
    }
  ]
}
```

## Response Example

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 2,
    "quizId": 2,
    "quizName": "test2",
    "studentId": 5,
    "studentName": "John Doe",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 900,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T18:15:38.476Z",
    "autoGraded": true,
    "grade": 9,
    "finalGrade": 9,
    "answers": [
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 1,
        "userAnswer": "gf",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 3,
        "userAnswer": "gf",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "hgfd",
        "questionGrade": 5,
        "userAnswer": "hgf",
        "isCorrect": true
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 9,
      "totalPossiblePoints": 9,
      "totalQuizPoints": 9,
      "percentageScore": 100
    }
  }
}
```

## Important Notes

1. **Question Matching**:
   - The server matches questions by their text content, not by ID
   - Make sure the question text in the request exactly matches the question text in the quiz content

2. **Auto-Grading**:
   - Since all questions are MCQ, the quiz is auto-graded
   - The server compares the selected answer ID with the correct answer ID from the quiz content
   - The grade is calculated based on the points earned from correct answers

3. **Answer Structure**:
   - Each answer includes the question text, grade, and user's selected answer
   - The `isCorrect` flag is added by the server based on the comparison with the quiz content
