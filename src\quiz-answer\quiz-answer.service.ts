import { Injectable, Logger, NotFoundException, BadRequestException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateQuizAnswerDto, GradeQuizAnswerDto } from './dto';

@Injectable()
export class QuizAnswerService {
  private readonly logger = new Logger(QuizAnswerService.name);

  constructor(private prisma: PrismaService) {}

  async submitQuizAnswer(createQuizAnswerDto: CreateQuizAnswerDto) {
    try {
      if (!createQuizAnswerDto.studentId) {
        throw new BadRequestException('Student ID is required');
      }

      // Verify that the quiz exists and get all necessary information
      const quiz = await this.prisma.quiz.findUnique({
        where: { id: createQuizAnswerDto.quizId },
        select: {
          id: true,
          name: true,
          numberOfAttempts: true,
          grade: true,
          content: true,
          subjectId: true,
          lessonId: true,
        },
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${createQuizAnswerDto.quizId} not found`);
      }

      // Verify that the student exists
      const student = await this.prisma.student.findUnique({
        where: { id: createQuizAnswerDto.studentId },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${createQuizAnswerDto.studentId} not found`);
      }

      // Count how many times the student has already submitted answers for this quiz
      const submissionCount = await this.prisma.quizAnswer.count({
        where: {
          quizId: createQuizAnswerDto.quizId,
          studentId: createQuizAnswerDto.studentId,
        },
      });

      // Check if the student has exceeded the allowed number of attempts
      // Skip this check if this is a redo submission
      if (!createQuizAnswerDto.redo && submissionCount >= quiz.numberOfAttempts) {
        throw new BadRequestException(`You have already used all ${quiz.numberOfAttempts} attempts for this quiz`);
      }

      // If this is a redo submission, delete the redo entry from the Redo table
      if (createQuizAnswerDto.redo) {
        await this.prisma.redo.deleteMany({
          where: {
            quizId: createQuizAnswerDto.quizId,
            studentId: createQuizAnswerDto.studentId,
          },
        });
        this.logger.debug(`Deleted redo entry for quiz ${createQuizAnswerDto.quizId} and student ${createQuizAnswerDto.studentId}`);
      }

      // Get the current semester (assuming there's only one active semester)
      const currentSemester = await this.prisma.semester.findFirst({
        where: { isCurrent: true },
        select: { id: true },
      });

      if (!currentSemester) {
        throw new BadRequestException('No active semester found');
      }

      // Get the current week based on the semester
      const currentWeek = await this.prisma.week.findFirst({
        where: {
          semesterId: currentSemester.id,
          startDate: {
            lte: new Date(),
          },
          endDate: {
            gte: new Date(),
          },
        },
        select: {
          id: true,
          weekNo: true // Using weekNo instead of weekNumber
        },
      });

      if (!currentWeek) {
        throw new BadRequestException('No active week found for the current semester');
      }

      // Parse quiz content
      const quizContent = typeof quiz.content === 'string'
        ? JSON.parse(quiz.content)
        : quiz.content;

      // Process answers and auto-grade MCQ questions
      let totalAutoGradedPoints = 0;
      let totalPossiblePoints = 0;

      // Create a map of question text to quiz content for matching
      // Since questions can be randomized, we can't rely on index/questionId matching
      const questionTextToContentMap = new Map();
      quizContent.forEach((q: any) => {
        questionTextToContentMap.set(q.question, q);
      });

      this.logger.debug(`Quiz content map created with ${questionTextToContentMap.size} questions`);

      const processedAnswers = createQuizAnswerDto.answers.map(answer => {
        // For MCQ questions, check if the answer is correct by comparing with the quiz content
        if (answer.type === 'mcq') {
          const mcqAnswer = answer as any; // Type assertion for easier access

          // Get the question from the map using question text for matching
          const question = questionTextToContentMap.get(answer.question);

          this.logger.debug(`Checking MCQ answer for question ID: ${mcqAnswer.questionId}, text: ${answer.question}`);
          this.logger.debug(`Found matching question in quiz content: ${question ? 'Yes' : 'No'}`);

          if (question) {
            this.logger.debug(`Question from content: ${question.question}`);
            this.logger.debug(`Question correct answer ID: ${question.correctAnswerId}`);
            this.logger.debug(`User selected answer ID: ${mcqAnswer.selectedAnswerId}`);
          }

          totalPossiblePoints += answer.questionGrade;

          let isCorrect = false;

          // If we found the question in the quiz content, check if the answer is correct
          if (question && question.type === 'mcq') {
            console.log("mcqAnswer selected by user", mcqAnswer.selectedAnswerId);
            console.log("question correct answer ID", question.correctAnswerId);
            // First try to use selectedAnswerId if provided
            if (mcqAnswer.selectedAnswerId !== undefined) {
              isCorrect = mcqAnswer.selectedAnswerId === question.correctAnswerId;
            } else {
              // If no selectedAnswerId, find the answer ID based on userAnswer text
              const correctAnswer = question.answers.find((ans: any) => ans.id === question.correctAnswerId);
              if (correctAnswer) {
                isCorrect = mcqAnswer.userAnswer === correctAnswer.text;
                this.logger.debug(`Comparing user answer "${mcqAnswer.userAnswer}" with correct answer "${correctAnswer.text}"`);
              }
            }

            this.logger.debug(`Answer is correct: ${isCorrect}`);
            if (isCorrect) {
              totalAutoGradedPoints += answer.questionGrade;
            }
          }

          return {
            type: 'mcq',
            question: answer.question,
            questionGrade: answer.questionGrade,
            userAnswer: mcqAnswer.userAnswer,
            isCorrect: isCorrect
          };
        }

        // For text questions
        if (answer.type === 'text') {
          const textAnswer = answer as any;
          return {
            type: 'text',
            question: answer.question,
            questionGrade: answer.questionGrade,
            userAnswer: textAnswer.userAnswer
          };
        }

        // For recording questions
        if (answer.type === 'record') {
          const recordAnswer = answer as any;
          return {
            type: 'record',
            question: answer.question,
            questionGrade: answer.questionGrade,
            userAnswer: recordAnswer.userAnswer
          };
        }

        // Fallback for any other type
        return {
          type: answer.type,
          question: answer.question,
          questionGrade: answer.questionGrade,
          userAnswer: "Unknown format"
        };
      });

      // Calculate the final grade (always the quiz's total grade)
      const finalGrade = quiz.grade;

      // Determine if we can auto-grade (only if all questions are MCQ)
      const allQuestionsMCQ = quizContent.every((q: any) => q.type === 'mcq');

      // Create the quiz answer with all required fields
      const quizAnswer = await this.prisma.quizAnswer.create({
        data: {
          quizId: createQuizAnswerDto.quizId,
          studentId: createQuizAnswerDto.studentId,
          semesterId: currentSemester.id,
          lessonId: quiz.lessonId || 0, // Fallback to 0 if not available
          weekId: currentWeek.id,
          subjectId: quiz.subjectId,
          answers: JSON.stringify(processedAnswers),
          timeTaken: createQuizAnswerDto.timeTaken,
          autoGraded: allQuestionsMCQ, // Only auto-grade if all questions are MCQ
          finalGrade: finalGrade,
          grade: allQuestionsMCQ ? totalAutoGradedPoints : null, // Only set grade if auto-graded
        },
        include: {
          quiz: {
            select: {
              id: true,
              name: true,
              grade: true,
              content: true,
            },
          },
          student: {
            select: {
              id: true,
              studentCode: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          semester: {
            select: {
              id: true,
              name: true,
            },
          },
          lesson: {
            select: {
              id: true,
              name: true,
            },
          },
          week: {
            select: {
              id: true,
              weekNo: true,
            },
          },
          subject: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      // Calculate the attempt number for this submission
      const attemptNumber = submissionCount + 1;

      // Format the response with proper type handling
      const parsedAnswers = typeof quizAnswer.answers === 'string'
        ? JSON.parse(quizAnswer.answers)
        : quizAnswer.answers;

      return {
        message: 'Quiz answers submitted successfully',
        data: {
          id: quizAnswer.id,
          quizId: quizAnswer.quizId,
          quizName: quizAnswer.quiz.name,
          studentId: quizAnswer.studentId,
          studentName: quizAnswer.student.user.name,
          studentCode: quizAnswer.student.studentCode,
          semesterId: quizAnswer.semesterId,
          semesterName: quizAnswer.semester.name,
          lessonId: quizAnswer.lessonId,
          lessonName: quizAnswer.lesson.name,
          weekId: quizAnswer.weekId,
          weekNumber: quizAnswer.week.weekNo, // Using weekNo instead of weekNumber
          subjectId: quizAnswer.subjectId,
          subjectName: quizAnswer.subject.name,
          timeTaken: quizAnswer.timeTaken,
          attemptNumber,
          submissionDate: quizAnswer.createdAt,
          autoGraded: quizAnswer.autoGraded,
          grade: quizAnswer.grade,
          finalGrade: quizAnswer.finalGrade,
          answers: parsedAnswers,
          gradingSummary: {
            totalAutoGradedPoints,
            totalPossiblePoints,
            totalQuizPoints: quiz.grade,
            percentageScore: Math.round((totalAutoGradedPoints / quiz.grade) * 100),
          }
        },
      };
    } catch (error) {
      this.logger.error(`Failed to submit quiz answer: ${error.message}`);
      throw error;
    }
  }



  async getQuizAnswersByQuizId(quizId: number) {
    try {
      const quizAnswers = await this.prisma.quizAnswer.findMany({
        where: { quizId },
        include: {
          student: {
            select: {
              studentCode: true,
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          quiz: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Quiz answers retrieved successfully',
        data: quizAnswers.map(answer => ({
          id: answer.id,
          studentName: answer.student.user.name,
          studentCode: answer.student.studentCode,
          quizName: answer.quiz.name,
          finalGrade: answer.finalGrade,
          grade: answer.grade,
          timeTaken: answer.timeTaken,
          autoGraded: answer.autoGraded,
          createdAt: answer.createdAt,
        })),
      };
    } catch (error) {
      this.logger.error(`Failed to get quiz answers by quiz ID: ${error.message}`);
      throw error;
    }
  }

  async getQuizAnswerById(id: number) {
    try {
      const quizAnswer = await this.prisma.quizAnswer.findUnique({
        where: { id },
        include: {
          quiz: {
            select: {
              id: true,
              name: true,
              grade: true,
              content: true,
              type: true,
              numberOfAttempts: true,
              timeLimit: true,
            },
          },
          student: {
            select: {
              id: true,
              studentCode: true,
              user: {
                select: {
                  id: true,
                  name: true,
                  email: true,
                },
              },
            },
          },
          semester: {
            select: {
              id: true,
              name: true,
            },
          },
          lesson: {
            select: {
              id: true,
              name: true,
            },
          },
          week: {
            select: {
              id: true,
              weekNo: true,
            },
          },
          subject: {
            select: {
              id: true,
              name: true,
            },
          },
        },
      });

      if (!quizAnswer) {
        throw new NotFoundException(`Quiz answer with ID ${id} not found`);
      }

      // Parse the answers JSON
      const parsedAnswers = typeof quizAnswer.answers === 'string'
        ? JSON.parse(quizAnswer.answers)
        : quizAnswer.answers;

      // Calculate the attempt number
      const attemptNumber = await this.prisma.quizAnswer.count({
        where: {
          quizId: quizAnswer.quizId,
          studentId: quizAnswer.studentId,
          createdAt: {
            lte: quizAnswer.createdAt,
          },
        },
      });

      return {
        message: 'Quiz answer details retrieved successfully',
        data: {
          id: quizAnswer.id,
          quizId: quizAnswer.quizId,
          quizName: quizAnswer.quiz.name,
          quizType: quizAnswer.quiz.type,
          studentId: quizAnswer.studentId,
          studentName: quizAnswer.student.user.name,
          studentCode: quizAnswer.student.studentCode,
          studentEmail: quizAnswer.student.user.email,
          semesterId: quizAnswer.semesterId,
          semesterName: quizAnswer.semester.name,
          lessonId: quizAnswer.lessonId,
          lessonName: quizAnswer.lesson.name,
          weekId: quizAnswer.weekId,
          weekNumber: quizAnswer.week.weekNo,
          subjectId: quizAnswer.subjectId,
          subjectName: quizAnswer.subject.name,
          timeTaken: quizAnswer.timeTaken,
          timeLimit: quizAnswer.quiz.timeLimit,
          attemptNumber,
          maxAttempts: quizAnswer.quiz.numberOfAttempts,
          submissionDate: quizAnswer.createdAt,
          autoGraded: quizAnswer.autoGraded,
          grade: quizAnswer.grade,
          finalGrade: quizAnswer.finalGrade,
          answers: parsedAnswers,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to get quiz answer details: ${error.message}`);
      throw error;
    }
  }

  async getQuizAnswersSummary(filters: {
    quizId?: number;
    lessonId?: number;
    subjectId?: number;
    weekId?: number;
    semesterId?: number;
  }) {
    try {
      // Build the where clause based on the provided filters
      const where: any = {};

      if (filters.quizId) {
        where.quizId = filters.quizId;
      }

      if (filters.lessonId) {
        where.lessonId = filters.lessonId;
      }

      if (filters.subjectId) {
        where.subjectId = filters.subjectId;
      }

      if (filters.weekId) {
        where.weekId = filters.weekId;
      }

      if (filters.semesterId) {
        where.semesterId = filters.semesterId;
      }

      const quizAnswers = await this.prisma.quizAnswer.findMany({
        where,
        include: {
          student: {
            select: {
              studentCode: true,
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
          quiz: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Quiz answers summary retrieved successfully',
        data: quizAnswers.map(answer => ({
          id: answer.id,
          studentName: answer.student.user.name,
          studentCode: answer.student.studentCode,
          quizName: answer.quiz.name,
          finalGrade: answer.finalGrade,
          grade: answer.grade,
          timeTaken: answer.timeTaken,
          autoGraded: answer.autoGraded,
          createdAt: answer.createdAt,
        })),
      };
    } catch (error) {
      this.logger.error(`Failed to get quiz answers summary: ${error.message}`);
      throw error;
    }
  }

  async gradeQuizAnswer(id: number, gradeQuizAnswerDto: GradeQuizAnswerDto) {
    try {
      // Find the quiz answer
      const quizAnswer = await this.prisma.quizAnswer.findUnique({
        where: { id },
        include: {
          quiz: {
            select: {
              name: true,
              grade: true,
            },
          },
          student: {
            select: {
              studentCode: true,
              user: {
                select: {
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      if (!quizAnswer) {
        throw new NotFoundException(`Quiz answer with ID ${id} not found`);
      }

      // Ensure the grade is not higher than the maximum grade for the quiz
      if (gradeQuizAnswerDto.grade > quizAnswer.quiz.grade) {
        throw new BadRequestException(`Grade cannot be higher than the maximum grade (${quizAnswer.quiz.grade})`);
      }

      // Update the quiz answer with the grade
      const updatedQuizAnswer = await this.prisma.quizAnswer.update({
        where: { id },
        data: {
          grade: gradeQuizAnswerDto.grade,
          autoGraded: false, // Set to false since it's manually graded
        },
        include: {
          quiz: {
            select: {
              name: true,
              grade: true,
            },
          },
          student: {
            select: {
              studentCode: true,
              user: {
                select: {
                  name: true,
                  email: true,
                },
              },
            },
          },
        },
      });

      // Parse the answers JSON
      const parsedAnswers = typeof updatedQuizAnswer.answers === 'string'
        ? JSON.parse(updatedQuizAnswer.answers)
        : updatedQuizAnswer.answers;

      return {
        message: 'Quiz answer graded successfully',
        data: {
          id: updatedQuizAnswer.id,
          quizId: updatedQuizAnswer.quizId,
          quizName: updatedQuizAnswer.quiz.name,
          studentName: updatedQuizAnswer.student.user.name,
          studentCode: updatedQuizAnswer.student.studentCode,
          grade: updatedQuizAnswer.grade,
          finalGrade: updatedQuizAnswer.finalGrade,
          autoGraded: updatedQuizAnswer.autoGraded,
          answers: parsedAnswers,
          updatedAt: updatedQuizAnswer.updatedAt,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to grade quiz answer: ${error.message}`);
      throw error;
    }
  }

  async addRedoEntry(studentId: number, quizId: number) {
    try {
      // Check if the student exists
      const student = await this.prisma.student.findUnique({
        where: { id: studentId },
        select: {
          id: true,
          studentCode: true,
          user: {
            select: {
              name: true,
            },
          },
        },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Check if the quiz exists
      const quiz = await this.prisma.quiz.findUnique({
        where: { id: quizId },
        select: {
          id: true,
          name: true,
        },
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${quizId} not found`);
      }

      // Check if the combination already exists
      const existingRedo = await this.prisma.redo.findFirst({
        where: {
          studentId,
          quizId,
        },
      });

      if (existingRedo) {
        throw new BadRequestException(`Redo entry already exists for student ${studentId} and quiz ${quizId}`);
      }

      // Create the redo entry
      const redoEntry = await this.prisma.redo.create({
        data: {
          studentId,
          quizId,
        },
      });

      return {
        message: 'Redo entry added successfully',
        data: {
          id: redoEntry.id,
          studentId: redoEntry.studentId,
          studentName: student.user.name,
          studentCode: student.studentCode,
          quizId: redoEntry.quizId,
          quizName: quiz.name,
          createdAt: redoEntry.createdAt,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to add redo entry: ${error.message}`);
      throw error;
    }
  }

  async getUserAnswersWithQuestions(userId: number) {
    try {
      // Get the student ID from the user ID
      const student = await this.prisma.student.findUnique({
        where: { userId },
        select: {
          id: true,
          studentCode: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!student) {
        throw new NotFoundException(`Student with user ID ${userId} not found`);
      }

      // Get all quiz answers for this student
      const quizAnswers = await this.prisma.quizAnswer.findMany({
        where: { studentId: student.id },
        include: {
          quiz: {
            select: {
              id: true,
              name: true,
              grade: true,
              content: true,
            },
          },
        },
        orderBy: { createdAt: 'desc' },
      });

      // Process each quiz answer to combine questions and answers
      const processedAnswers = await Promise.all(quizAnswers.map(async (quizAnswer) => {
        // Parse the answers and quiz content from JSON strings if needed
        const parsedAnswers = typeof quizAnswer.answers === 'string'
          ? JSON.parse(quizAnswer.answers)
          : quizAnswer.answers;

        const parsedQuizContent = typeof quizAnswer.quiz.content === 'string'
          ? JSON.parse(quizAnswer.quiz.content)
          : quizAnswer.quiz.content;

        // Auto-grade MCQ questions
        let totalGrade = 0;
        let maxPossibleGrade = 0;

        // Combine quiz questions with student answers for easier grading
        const combinedData = parsedQuizContent.map((question: any) => {
          const studentAnswer = parsedAnswers.find((answer: any) => answer.questionId === question.id);

          // Auto-grade MCQ questions
          let isCorrect = false;
          if (studentAnswer && studentAnswer.type === 'mcq' && question.type === 'mcq') {
            // Add the question's grade to the maximum possible grade
            maxPossibleGrade += question.grade || 0;

            // Check if the answer is correct
            if (studentAnswer.selectedAnswerId === question.correctAnswerId) {
              totalGrade += question.grade || 0;
              isCorrect = true;
            }
          }

          return {
            question,
            studentAnswer: studentAnswer || null,
            autoGraded: studentAnswer && studentAnswer.type === 'mcq',
            isCorrect: isCorrect,
          };
        });

        // Calculate the percentage of correct MCQ answers
        const percentageCorrect = maxPossibleGrade > 0 ? (totalGrade / maxPossibleGrade) : 0;

        return {
          id: quizAnswer.id,
          quizId: quizAnswer.quizId,
          quizName: quizAnswer.quiz.name,
          grade: quizAnswer.grade,
          createdAt: quizAnswer.createdAt,
          updatedAt: quizAnswer.updatedAt,
          combinedData,
          autoGradingDetails: {
            totalCorrectPoints: totalGrade,
            maxPossiblePoints: maxPossibleGrade,
            percentageCorrect: Math.round(percentageCorrect * 100),
          }
        };
      }));

      return {
        message: 'User quiz answers with questions retrieved successfully',
        data: {
          student,
          quizAnswers: processedAnswers,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to fetch user answers with questions: ${error.message}`);
      throw error;
    }
  }

  async getQuizAnswersSummaryByQuizId(quizId: number) {
    try {
      // Verify quiz exists
      const quiz = await this.prisma.quiz.findUnique({
        where: { id: quizId },
        select: {
          id: true,
          name: true,
        },
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${quizId} not found`);
      }

      // Get quiz answers with basic student info, grouped by student to get latest attempt
      const quizAnswers = await this.prisma.quizAnswer.findMany({
        where: { quizId },
        include: {
          student: {
            select: {
              studentCode: true,
              user: {
                select: {
                  name: true,
                },
              },
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Group by student and get latest attempt for each
      const studentAnswersMap = new Map();

      quizAnswers.forEach(answer => {
        const studentId = answer.studentId;
        if (!studentAnswersMap.has(studentId)) {
          studentAnswersMap.set(studentId, {
            id: answer.id,
            studentName: answer.student.user.name,
            studentCode: answer.student.studentCode,
            attemptNumber: 1,
            date: answer.createdAt,
            totalAttempts: 1,
          });
        } else {
          // Increment attempt count for this student
          const existing = studentAnswersMap.get(studentId);
          existing.totalAttempts += 1;
          // Keep the latest attempt info
          if (answer.createdAt > existing.date) {
            existing.id = answer.id;
            existing.date = answer.createdAt;
            existing.attemptNumber = existing.totalAttempts;
          }
        }
      });

      // Convert map to array
      const summaryData = Array.from(studentAnswersMap.values()).map(item => ({
        id: item.id,
        studentName: item.studentName,
        studentCode: item.studentCode,
        attemptNumber: item.attemptNumber,
        date: item.date,
      }));

      return {
        message: 'Quiz answers summary retrieved successfully',
        data: {
          quiz: {
            id: quiz.id,
            name: quiz.name,
          },
          answers: summaryData,
        },
      };
    } catch (error) {
      this.logger.error(`Failed to fetch quiz answers summary for quiz ${quizId}: ${error.message}`);
      throw error;
    }
  }




}
