# Quiz Answer Module

This module provides functionality for students to submit answers to quizzes and for teachers/admins to grade those answers.

## Features

- Submit quiz answers (for students)
- Retrieve quiz answers with questions (for grading)
- Grade quiz answers (for teachers/admins)
- View quiz answers by quiz or student
- Get user answers with questions for correction (with auto-grading for MCQ questions)

## API Endpoints

### Submit Quiz Answer

```
POST /quiz-answers
```

Allows a student to submit answers to a quiz. The student ID is automatically extracted from the JWT token, so there's no need to include it in the request body.

**Request Body:**
```json
{
  "quizId": 1,
  "timeTaken": 300,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "selectedAnswerId": 0
    },
    {
      "type": "text",
      "questionId": 1,
      "text": "This is my answer to the text question"
    },
    {
      "type": "record",
      "questionId": 2,
      "recordingUrl": "https://example.com/recordings/abc123.mp3"
    }
  ]
}
```

### Get Quiz Answer by ID

```
GET /quiz-answers/:id
```

Retrieves a specific quiz answer with questions and student answers combined for grading.

### Get Quiz Answers by Quiz ID

```
GET /quiz-answers/quiz/:quizId
```

Retrieves all student answers for a specific quiz.

### Get Quiz Answers by Student ID

```
GET /quiz-answers/student/:studentId
```

Retrieves all quiz answers submitted by a specific student.

### Get User Answers with Questions

```
GET /quiz-answers/user/answers-with-questions
```

Retrieves all quiz answers for the authenticated user with questions for correction. MCQ questions are automatically graded.

### Grade Quiz Answer

```
PUT /quiz-answers/:id/grade
```

Grades a student's quiz submission.

**Request Body:**
```json
{
  "grade": 85
}
```

## Authentication

All endpoints require JWT authentication. The token must be included in the Authorization header.

## Error Handling

- If a quiz or student is not found, a 404 Not Found response is returned
- If a student tries to submit answers for a quiz more times than the allowed number of attempts, a 400 Bad Request is returned
- If an invalid grade is provided, a 400 Bad Request is returned

## Database Schema

The module adds a new `QuizAnswer` model to the database schema:

```prisma
model QuizAnswer {
  id               Int        @id @default(autoincrement())
  studentId        Int        // Foreign key to Student
  quizId           Int        // Foreign key to Quiz
  answers          Json       // JSON containing the student's answers
  grade            Int?       // Optional grade (null until graded)
  timeTaken        Int?       // Time taken in seconds
  autoGraded       Boolean    @default(false)
  createdAt        DateTime   @default(now())
  updatedAt        DateTime   @updatedAt

  student          Student    @relation(fields: [studentId], references: [id])
  quiz             quiz       @relation(fields: [quizId], references: [id])

  @@index([studentId])
  @@index([quizId])
}
```

## Testing

For detailed testing instructions and examples, see the `quiz-answer-api-docs.md` file in the root directory.
