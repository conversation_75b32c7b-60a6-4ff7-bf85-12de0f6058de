# Updated Quiz Answer API Examples

## Submit Quiz Answer

### Request

```<PERSON>son
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 1,
  "timeTaken": 1200,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "selectedAnswerId": 0
    },
    {
      "type": "text",
      "questionId": 1,
      "text": "This is my answer to the text question 'gfds'"
    },
    {
      "type": "mcq",
      "questionId": 2,
      "selectedAnswerId": 1
    }
  ]
}
```

### Response

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 1,
    "quizId": 1,
    "quizName": "test exam",
    "studentId": 5,
    "studentName": "<PERSON> Doe",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 1200,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T17:15:38.476Z",
    "autoGraded": false,
    "grade": null,
    "finalGrade": 9,
    "answers": [
      {
        "type": "mcq",
        "question": "fds",
        "questionGrade": 1,
        "userAnswer": "gfd",
        "isCorrect": true
      },
      {
        "type": "text",
        "question": "gfds",
        "questionGrade": 3,
        "userAnswer": "This is my answer to the text question 'gfds'",
        "correctAnswer": "fd"
      },
      {
        "type": "mcq",
        "question": "gfds",
        "questionGrade": 5,
        "userAnswer": "bvc",
        "isCorrect": false
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 1,
      "totalPossiblePoints": 6,
      "totalQuizPoints": 9,
      "percentageScore": 11
    }
  }
}
```

## Notes on the Implementation

1. **Auto-Grading Logic**:
   - `autoGraded` is set to `true` ONLY if ALL questions are MCQ
   - If there are any text or recording questions, `autoGraded` is set to `false` and `grade` is set to `null`
   - The quiz will need to be manually graded in this case

2. **Answer Structure**:
   - Each answer includes:
     - `type`: The type of question/answer (mcq, text, record)
     - `question`: The full question text
     - `questionGrade`: The grade value for this question
     - `userAnswer`: The user's answer (text for text questions, selected answer text for MCQ)
     - `isCorrect`: For MCQ questions only, whether the answer is correct

3. **Grade Fields**:
   - `grade`: The actual points earned (null if not auto-graded)
   - `finalGrade`: Always set to the quiz's total grade (9 in this example)

4. **Example for Quiz with Only MCQ Questions**:
   If the quiz had only MCQ questions, the response would have:
   - `autoGraded: true`
   - `grade`: The sum of points from correctly answered MCQ questions
   - `finalGrade`: The quiz's total grade

5. **Example for Quiz with Mixed Question Types**:
   As shown in the example above, with both MCQ and text questions:
   - `autoGraded: false`
   - `grade: null` (awaiting manual grading)
   - `finalGrade`: The quiz's total grade (9)

## Example: Quiz with Only MCQ Questions

### Request

```json
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 2,
  "timeTaken": 600,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "selectedAnswerId": 1
    },
    {
      "type": "mcq",
      "questionId": 1,
      "selectedAnswerId": 0
    }
  ]
}
```

### Response

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 2,
    "quizId": 2,
    "quizName": "MCQ Only Quiz",
    "studentId": 5,
    "studentName": "John Doe",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 600,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T18:15:38.476Z",
    "autoGraded": true,
    "grade": 3,
    "finalGrade": 5,
    "answers": [
      {
        "type": "mcq",
        "question": "First MCQ question",
        "questionGrade": 2,
        "userAnswer": "Option B",
        "isCorrect": false
      },
      {
        "type": "mcq",
        "question": "Second MCQ question",
        "questionGrade": 3,
        "userAnswer": "Option A",
        "isCorrect": true
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 3,
      "totalPossiblePoints": 5,
      "totalQuizPoints": 5,
      "percentageScore": 60
    }
  }
}
