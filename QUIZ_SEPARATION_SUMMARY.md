# Quiz Separation Summary

## Overview

Successfully separated the quiz functionality from the `getSubjectsAndLessonsByWeekId` endpoint in the semester controller and moved it to a dedicated quiz controller endpoint.

## Changes Made

### 1. Quiz Service (`src/quiz/quiz.service.ts`)
- **Added new method**: `getQuizzesByWeekId(weekId: number, studentId?: number)`
- **Extracted all quiz logic** from the semester service including:
  - Quiz retrieval by lesson IDs
  - Student attempt tracking
  - Quiz status determination (open, pending, excellent, pass, fail, closed, redo)
  - Redo table checking
  - Quiz-to-lesson mapping

### 2. Quiz Controller (`src/quiz/quiz.controller.ts`)
- **Added new endpoint**: `GET /quiz/week/:weekId`
- **Features**:
  - JWT authentication required
  - Automatically extracts student ID from authenticated user
  - Returns quizzes organized by lesson ID

### 3. Semester Service (`src/semester/semester.service.ts`)
- **Modified method**: `getSubjectsAndLessonsByWeekId(weekId: number)`
- **Removed**:
  - All quiz-related logic
  - Student ID parameter
  - Quiz attempt tracking
  - Quiz status determination
- **Now only returns**: Subjects and lessons without quiz data

### 4. Semester Controller (`src/semester/semester.controller.ts`)
- **Modified endpoint**: `GET /semester/week/:weekId/subjects`
- **Removed**:
  - JWT authentication requirement
  - Student ID extraction
  - Quiz-related functionality

## API Endpoints

### Original Combined Endpoint (Modified)
**Endpoint**: `GET /semester/week/:weekId/subjects`
**Authentication**: None required
**Purpose**: Get subjects and lessons for a week (without quizzes)

### New Separated Quiz Endpoint
**Endpoint**: `GET /quiz/week/:weekId`
**Authentication**: JWT required
**Purpose**: Get quizzes for a week with student-specific data

## Example Requests and Responses

### 1. Get Subjects and Lessons (Semester Endpoint)

#### Request
```http
GET /semester/week/1/subjects
```

#### Response
```json
{
  "message": "Subjects and lessons retrieved successfully",
  "data": {
    "subjects": [
      {
        "id": 1,
        "name": "Mathematics",
        "code": "MATH101",
        "lessons": [
          {
            "id": 1,
            "name": "Introduction to Algebra",
            "items": [
              {
                "id": 1,
                "name": "Algebra Basics",
                "type": "video",
                "content": "video_url_here"
              }
            ],
            "order": 1
          }
        ]
      },
      {
        "id": 2,
        "name": "Physics",
        "code": "PHYS101",
        "lessons": [
          {
            "id": 2,
            "name": "Newton's Laws",
            "items": [
              {
                "id": 2,
                "name": "First Law",
                "type": "document",
                "content": "document_url_here"
              }
            ],
            "order": 2
          }
        ]
      }
    ]
  }
}
```

### 2. Get Quizzes by Week (Quiz Endpoint)

#### Request
```http
GET /quiz/week/1
Authorization: Bearer <JWT_TOKEN>
```

#### Response
```json
{
  "message": "Quizzes retrieved successfully",
  "data": {
    "weekId": 1,
    "lessonQuizzesMap": {
      "1": [
        {
          "id": 1,
          "name": "Algebra Quiz 1",
          "timeLimit": 1800,
          "grade": 10,
          "numberOfAttempts": 3,
          "type": "mixed",
          "isRecord": false,
          "lessonId": 1,
          "order": 0,
          "userAttempts": 1,
          "status": "pass"
        },
        {
          "id": 2,
          "name": "Algebra Quiz 2",
          "timeLimit": 2400,
          "grade": 15,
          "numberOfAttempts": 2,
          "type": "mcq",
          "isRecord": false,
          "lessonId": 1,
          "order": 1,
          "userAttempts": 0,
          "status": "open"
        }
      ],
      "2": [
        {
          "id": 3,
          "name": "Physics Quiz 1",
          "timeLimit": 3600,
          "grade": 20,
          "numberOfAttempts": 2,
          "type": "text",
          "isRecord": false,
          "lessonId": 2,
          "order": 0,
          "userAttempts": 2,
          "status": "fail"
        }
      ]
    }
  }
}
```

## Quiz Status Values

- **open**: Quiz is available for attempts
- **pending**: Quiz submitted but not yet graded
- **excellent**: Student achieved full grade
- **pass**: Student achieved at least 50% of the grade
- **fail**: Student achieved less than 50% of the grade
- **closed**: Week has ended and no attempts were made
- **redo**: Quiz is available for redo (special permission granted)

## Benefits of Separation

1. **Single Responsibility**: Each endpoint has a clear, focused purpose
2. **Better Security**: Quiz data requires authentication, subjects/lessons don't
3. **Improved Performance**: Clients can fetch only the data they need
4. **Easier Maintenance**: Quiz logic is centralized in the quiz module
5. **Scalability**: Each endpoint can be optimized independently

## No Logic Lost

All original functionality has been preserved:
- ✅ Quiz retrieval by week
- ✅ Student attempt tracking
- ✅ Status determination logic
- ✅ Redo table checking
- ✅ Quiz-to-lesson mapping
- ✅ Subject and lesson retrieval
