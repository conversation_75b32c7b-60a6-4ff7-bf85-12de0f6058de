# Redo Quiz Answer API Example

## Submit Quiz Answer with Redo Flag

This endpoint allows students to submit quiz answers even if they have exceeded the maximum number of attempts, provided they have a redo entry in the Redo table.

### Endpoint

```
POST /quiz-answers
```

### Request Body with Redo Flag

```json
{
  "quizId": 3,
  "timeTaken": 900,
  "redo": true,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "question": "fd",
      "questionGrade": 1,
      "selectedAnswerId": 1,
      "userAnswer": "ds"
    },
    {
      "type": "mcq",
      "questionId": 1,
      "question": "gfds",
      "questionGrade": 3,
      "selectedAnswerId": 1,
      "userAnswer": "gh"
    },
    {
      "type": "mcq",
      "questionId": 2,
      "question": "hgfdsa",
      "questionGrade": 5,
      "selectedAnswerId": 1,
      "userAnswer": "gh"
    }
  ]
}
```

### Example Response

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 7,
    "quizId": 3,
    "quizName": "test2",
    "studentId": 5,
    "studentName": "<PERSON>",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 900,
    "attemptNumber": 2,
    "submissionDate": "2025-05-22T20:30:38.476Z",
    "autoGraded": true,
    "grade": 9,
    "finalGrade": 9,
    "answers": [
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 1,
        "userAnswer": "ds",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "gfds",
        "questionGrade": 3,
        "userAnswer": "gh",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "hgfdsa",
        "questionGrade": 5,
        "userAnswer": "gh",
        "isCorrect": true
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 9,
      "totalPossiblePoints": 9,
      "totalQuizPoints": 9,
      "percentageScore": 100
    }
  }
}
```

## How Redo Functionality Works

1. **Redo Flag**: When `redo: true` is included in the request body, the system:
   - Skips the check for maximum number of attempts
   - Allows the student to submit the quiz even if they have already used all their attempts
   - Deletes the corresponding entry from the Redo table after successful submission

2. **Redo Table Cleanup**: After a successful redo submission, the system automatically removes the redo entry from the database to prevent multiple redo attempts.

3. **Validation**: The redo flag is optional and defaults to `false` if not provided.

## Use Cases

1. **Failed Quiz with Max Attempts**: A student failed a quiz and used all their attempts, but a teacher granted them a redo opportunity.

2. **Closed Quiz**: A student missed the deadline for a quiz, but a teacher granted them a redo opportunity.

3. **Technical Issues**: A student experienced technical difficulties during their quiz attempt and was granted a redo.

## Notes

- The `redo` field is optional and should only be set to `true` when the student has a valid redo entry in the Redo table
- If `redo: true` is used but no redo entry exists in the database, the submission will still be accepted (the system doesn't validate the existence of the redo entry before submission)
- The redo entry is automatically deleted after successful submission to prevent abuse
- All other validation and grading logic remains the same as regular quiz submissions
