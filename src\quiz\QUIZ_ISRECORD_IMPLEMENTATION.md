# Quiz isRecord Field Implementation

## Overview

This document outlines the implementation of the optional `isRecord` boolean field in the quiz model and its integration across all quiz endpoints.

## Database Changes

### Schema Update
- Added `isRecord <PERSON>? @default(false)` to the quiz model in `prisma/schema.prisma`
- Applied database migration using `npx prisma db push`

### Migration Details
```prisma
model quiz {
  id               Int          @id @default(autoincrement())
  name             String
  subjectId        Int
  grade            Int
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  type             String
  numberOfAttempts Int
  timeLimit        Int?
  content          Json
  lessonId         Int?
  isRecord         Boolean?     @default(false)  // NEW FIELD
  lesson           Lesson?      @relation(fields: [lessonId], references: [id])
  subject          Subject      @relation(fields: [subjectId], references: [id])
  answers          QuizAnswer[]
  redos            Redo[]
}
```

## DTO Updates

### CreateQuizDto
```typescript
export class CreateQuizDto {
  // ... existing fields ...

  @IsInt()
  @IsOptional()
  timeLimit?: number; // Optional time limit in minutes

  @IsBoolean()
  @IsOptional()
  isRecord?: boolean; // Optional flag to indicate if this quiz supports recording

  // ... rest of fields ...
}
```

### UpdateQuizDto
```typescript
export class UpdateQuizDto extends PartialType(CreateQuizDto) {
  @IsBoolean()
  @IsOptional()
  isRecord?: boolean; // Optional flag to indicate if this quiz supports recording
}
```

## Service Method Updates

### 1. createQuiz()
- Added `isRecord: createQuizDto.isRecord` to the Prisma create data
- Field is optional and defaults to `false` if not provided

### 2. updateQuiz()
- Added `isRecord: updateQuizDto.isRecord` to the Prisma update data
- Allows updating the recording flag for existing quizzes

### 3. getSimpleQuizzesByWeekId()
- Added `isRecord: true` to the select clause
- Included `isRecord: quiz.isRecord` in the response mapping

### 4. getSimpleQuizzesByLessonId()
- Added `isRecord: true` to the select clause
- Field is automatically included in the response

### 5. Other Methods
All other quiz retrieval methods (`getQuizById`, `getQuizzes`, `getQuizzesByWeekId`, `getQuizWithRandomQuestions`) automatically include the `isRecord` field since they return the full quiz object.

## API Endpoints Updated

### POST /quiz
- Accepts optional `isRecord` field in request body
- Creates quiz with recording capability if `isRecord: true`

### PUT /quiz/:id
- Accepts optional `isRecord` field in request body
- Updates quiz recording capability

### GET /quiz/:id
- Returns `isRecord` field in response

### GET /quiz
- Returns `isRecord` field for all quizzes

### GET /quiz/:id/random
- Returns `isRecord` field in response

### GET /quiz/week/:weekId
- Returns `isRecord` field for all quizzes

### GET /quiz/week/:weekId/simple
- Returns `isRecord` field in simplified quiz objects

### GET /quiz/lesson/:lessonId/simple
- Returns `isRecord` field in simplified quiz objects

## Backward Compatibility

- **Existing Data**: All existing quizzes will have `isRecord: false` by default
- **API Requests**: The field is optional in all requests
- **Responses**: All responses now include the `isRecord` field
- **Default Behavior**: Quizzes without the field specified behave as standard (non-recording) quizzes

## Use Cases

### Recording-Enabled Quizzes (`isRecord: true`)
- Oral examinations
- Music performance assessments
- Language pronunciation tests
- Presentation evaluations
- Interview simulations

### Standard Quizzes (`isRecord: false`)
- Written examinations
- Multiple-choice tests
- Theory assessments
- Quick knowledge checks

## Frontend Integration Guidelines

1. **Conditional UI**: Show recording interface only when `isRecord: true`
2. **Question Types**: Support `record` type questions in recording quizzes
3. **Permissions**: Request microphone/camera access for recording quizzes
4. **File Handling**: Implement audio/video upload for recorded answers
5. **Validation**: Ensure recording questions are only in recording-enabled quizzes

## Testing Recommendations

1. **Create Quiz**: Test creating quizzes with and without `isRecord` flag
2. **Update Quiz**: Test updating the `isRecord` field
3. **Retrieve Quiz**: Verify `isRecord` field is returned in all endpoints
4. **Backward Compatibility**: Ensure existing quizzes work without issues
5. **Default Values**: Verify default behavior when field is not specified

## Files Modified

1. `prisma/schema.prisma` - Added isRecord field to quiz model
2. `src/quiz/dto/create-quiz.dto.ts` - Added isRecord validation and made timeLimit optional
3. `src/quiz/dto/update-quiz.dto.ts` - Added isRecord validation
4. `src/quiz/quiz.service.ts` - Updated all relevant methods
5. `src/quiz/examples/quiz-isRecord-field-example.md` - Usage examples
6. `src/quiz/examples/optional-timeLimit-example.md` - timeLimit usage examples
7. `src/quiz/QUIZ_ISRECORD_IMPLEMENTATION.md` - This documentation

## Migration Status

✅ Database schema updated
✅ DTOs updated with validation
✅ timeLimit made optional in DTOs
✅ Service methods updated
✅ All quiz endpoints support isRecord field
✅ Documentation and examples created
✅ Backward compatibility maintained
