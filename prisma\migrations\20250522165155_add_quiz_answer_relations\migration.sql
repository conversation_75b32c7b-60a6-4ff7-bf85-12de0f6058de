/*
  Warnings:

  - You are about to drop the column `semesterId` on the `quiz` table. All the data in the column will be lost.
  - You are about to drop the `WeekQuiz` table. If the table is not empty, all the data it contains will be lost.
  - Added the required column `finalGrade` to the `quizAnswer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `lessonId` to the `quizAnswer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `semesterId` to the `quizAnswer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `subjectId` to the `quizAnswer` table without a default value. This is not possible if the table is not empty.
  - Added the required column `weekId` to the `quizAnswer` table without a default value. This is not possible if the table is not empty.

*/
-- DropForeignKey
ALTER TABLE "WeekQuiz" DROP CONSTRAINT "WeekQuiz_quizId_fkey";

-- DropForeignKey
ALTER TABLE "WeekQuiz" DROP CONSTRAINT "WeekQuiz_weekId_fkey";

-- DropForeignKey
ALTER TABLE "quiz" DROP CONSTRAINT "quiz_semesterId_fkey";

-- AlterTable
ALTER TABLE "quiz" DROP COLUMN "semesterId";

-- AlterTable
ALTER TABLE "quizAnswer" ADD COLUMN     "finalGrade" INTEGER NOT NULL,
ADD COLUMN     "lessonId" INTEGER NOT NULL,
ADD COLUMN     "semesterId" INTEGER NOT NULL,
ADD COLUMN     "subjectId" INTEGER NOT NULL,
ADD COLUMN     "weekId" INTEGER NOT NULL;

-- DropTable
DROP TABLE "WeekQuiz";

-- CreateIndex
CREATE INDEX "quizAnswer_semesterId_idx" ON "quizAnswer"("semesterId");

-- CreateIndex
CREATE INDEX "quizAnswer_lessonId_idx" ON "quizAnswer"("lessonId");

-- CreateIndex
CREATE INDEX "quizAnswer_weekId_idx" ON "quizAnswer"("weekId");

-- CreateIndex
CREATE INDEX "quizAnswer_subjectId_idx" ON "quizAnswer"("subjectId");

-- AddForeignKey
ALTER TABLE "quizAnswer" ADD CONSTRAINT "quizAnswer_semesterId_fkey" FOREIGN KEY ("semesterId") REFERENCES "semesters"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quizAnswer" ADD CONSTRAINT "quizAnswer_lessonId_fkey" FOREIGN KEY ("lessonId") REFERENCES "Lesson"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quizAnswer" ADD CONSTRAINT "quizAnswer_weekId_fkey" FOREIGN KEY ("weekId") REFERENCES "Week"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quizAnswer" ADD CONSTRAINT "quizAnswer_subjectId_fkey" FOREIGN KEY ("subjectId") REFERENCES "Subject"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
