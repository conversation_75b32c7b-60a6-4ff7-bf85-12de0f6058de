import { IsOptional, IsString, IsInt, IsArray } from 'class-validator';

export class UpdateAnnouncementDto {
  @IsOptional()
  @IsString({ message: 'Title must be a string' })
  title?: string;

  @IsOptional()
  @IsString({ message: 'Description must be a string' })
  description?: string;

  @IsOptional()
  @IsString({ message: 'Meeting link must be a string' })
  meetingLink?: string;

  @IsOptional()
  @IsString({ message: 'Image URL must be a string' })
  imageUrl?: string;

  @IsOptional()
  @IsArray({ message: 'Week IDs must be an array' })
  @IsInt({ each: true, message: 'Each week ID must be an integer' })
  weekIds?: number[];
}
