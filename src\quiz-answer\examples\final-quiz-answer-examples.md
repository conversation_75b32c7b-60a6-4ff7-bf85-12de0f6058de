# Final Quiz Answer API Examples

## Submit Quiz Answer

### Request (Mixed Question Types)

```json
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 1,
  "timeTaken": 1200,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "question": "fds",
      "questionGrade": 1,
      "selectedAnswerId": 0,
      "userAnswer": "gfd"
    },
    {
      "type": "text",
      "questionId": 1,
      "question": "gfds",
      "questionGrade": 3,
      "userAnswer": "This is my answer to the text question 'gfds'"
    },
    {
      "type": "mcq",
      "questionId": 2,
      "question": "gfds",
      "questionGrade": 5,
      "selectedAnswerId": 1,
      "userAnswer": "bvc"
    }
  ]
}
```

### Response (Mixed Question Types)

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 1,
    "quizId": 1,
    "quizName": "test exam",
    "studentId": 5,
    "studentName": "<PERSON>",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 1200,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T17:15:38.476Z",
    "autoGraded": false,
    "grade": null,
    "finalGrade": 9,
    "answers": [
      {
        "type": "mcq",
        "question": "fds",
        "questionGrade": 1,
        "userAnswer": "gfd",
        "isCorrect": true
      },
      {
        "type": "text",
        "question": "gfds",
        "questionGrade": 3,
        "userAnswer": "This is my answer to the text question 'gfds'"
      },
      {
        "type": "mcq",
        "question": "gfds",
        "questionGrade": 5,
        "userAnswer": "bvc",
        "isCorrect": false
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 1,
      "totalPossiblePoints": 6,
      "totalQuizPoints": 9,
      "percentageScore": 11
    }
  }
}
```

### Request (MCQ Only Questions)

```json
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 2,
  "timeTaken": 600,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "question": "First MCQ question",
      "questionGrade": 2,
      "selectedAnswerId": 1,
      "userAnswer": "Option B"
    },
    {
      "type": "mcq",
      "questionId": 1,
      "question": "Second MCQ question",
      "questionGrade": 3,
      "selectedAnswerId": 0,
      "userAnswer": "Option A"
    }
  ]
}
```

### Response (MCQ Only Questions)

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 2,
    "quizId": 2,
    "quizName": "MCQ Only Quiz",
    "studentId": 5,
    "studentName": "John Doe",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 600,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T18:15:38.476Z",
    "autoGraded": true,
    "grade": 3,
    "finalGrade": 5,
    "answers": [
      {
        "type": "mcq",
        "question": "First MCQ question",
        "questionGrade": 2,
        "userAnswer": "Option B",
        "isCorrect": false
      },
      {
        "type": "mcq",
        "question": "Second MCQ question",
        "questionGrade": 3,
        "userAnswer": "Option A",
        "isCorrect": true
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 3,
      "totalPossiblePoints": 5,
      "totalQuizPoints": 5,
      "percentageScore": 60
    }
  }
}
```

## Notes on the Implementation

1. **Request Format**:
   - The client now sends the question text and grade in the request
   - For MCQ questions, the client also sends the selected answer text
   - This allows the server to store the complete question and answer information

2. **Auto-Grading Logic**:
   - `autoGraded` is set to `true` ONLY if ALL questions are MCQ
   - If there are any text or recording questions, `autoGraded` is set to `false` and `grade` is set to `null`
   - The quiz will need to be manually graded in this case

3. **Grade Fields**:
   - `grade`: The actual points earned (null if not auto-graded)
   - `finalGrade`: Always set to the quiz's total grade

4. **MCQ Validation**:
   - For MCQ questions, the server still checks the quiz content to determine if the answer is correct
   - The `isCorrect` flag is set based on comparing the selected answer ID with the correct answer ID from the quiz
