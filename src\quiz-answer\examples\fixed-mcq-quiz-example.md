# Fixed MCQ Quiz Example

## Quiz Content from Database

```json
[
  {
    "question": "fd",
    "type": "mcq",
    "grade": 1,
    "answers": [
      {"id": 0, "text": "gfd"},
      {"id": 1, "text": "gf"}
    ],
    "correctAnswerId": 1
  },
  {
    "question": "fd",
    "type": "mcq",
    "grade": 3,
    "answers": [
      {"id": 0, "text": "gf"},
      {"id": 1, "text": "gfd"}
    ],
    "correctAnswerId": 0
  },
  {
    "question": "hgfd",
    "type": "mcq",
    "grade": 5,
    "answers": [
      {"id": 0, "text": "gf"},
      {"id": 1, "text": "hgf"}
    ],
    "correctAnswerId": 1
  }
]
```

## Request Example

```json
POST /quiz-answers
Authorization: Bearer <JWT_TOKEN>

{
  "quizId": 2,
  "timeTaken": 900,
  "answers": [
    {
      "type": "mcq",
      "questionId": 0,
      "question": "fd",
      "questionGrade": 1,
      "selectedAnswerId": 1,
      "userAnswer": "gf"
    },
    {
      "type": "mcq",
      "questionId": 1,
      "question": "fd",
      "questionGrade": 3,
      "selectedAnswerId": 0,
      "userAnswer": "gf"
    },
    {
      "type": "mcq",
      "questionId": 2,
      "question": "hgfd",
      "questionGrade": 5,
      "selectedAnswerId": 1,
      "userAnswer": "hgf"
    }
  ]
}
```

## Response Example

```json
{
  "message": "Quiz answers submitted successfully",
  "data": {
    "id": 2,
    "quizId": 2,
    "quizName": "test2",
    "studentId": 5,
    "studentName": "John Doe",
    "studentCode": "ST12345",
    "semesterId": 1,
    "semesterName": "Spring 2025",
    "lessonId": 1,
    "lessonName": "lesson1",
    "weekId": 2,
    "weekNumber": 3,
    "subjectId": 1,
    "subjectName": "الحان",
    "timeTaken": 900,
    "attemptNumber": 1,
    "submissionDate": "2025-05-22T18:15:38.476Z",
    "autoGraded": true,
    "grade": 9,
    "finalGrade": 9,
    "answers": [
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 1,
        "userAnswer": "gf",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 3,
        "userAnswer": "gf",
        "isCorrect": true
      },
      {
        "type": "mcq",
        "question": "hgfd",
        "questionGrade": 5,
        "userAnswer": "hgf",
        "isCorrect": true
      }
    ],
    "gradingSummary": {
      "totalAutoGradedPoints": 9,
      "totalPossiblePoints": 9,
      "totalQuizPoints": 9,
      "percentageScore": 100
    }
  }
}
```

## Important Notes

1. **Question ID Matching**:
   - The `questionId` in the request should match the index of the question in the quiz content array
   - For the first question in the quiz content, use `questionId: 0`
   - For the second question, use `questionId: 1`, and so on
   - This is critical for correct auto-grading, especially when there are questions with the same text

2. **Auto-Grading Logic**:
   - The server creates a map of question indices to quiz content questions
   - It then uses the `questionId` from the request to look up the correct question in the map
   - The selected answer ID is compared with the correct answer ID from the quiz content
   - If they match, the answer is marked as correct and points are awarded

3. **Example Explanation**:
   - In this example, all three answers are correct:
     - Question 0: Selected answer ID 1, which matches the correct answer ID
     - Question 1: Selected answer ID 0, which matches the correct answer ID
     - Question 2: Selected answer ID 1, which matches the correct answer ID
   - Since all questions are MCQ and all answers are correct, the grade is 9 (full points)
