import { Body, Controller, Delete, Get, Param, Post, Put, UseGuards, Logger, HttpException, HttpStatus, ParseIntPipe, HttpCode } from '@nestjs/common';
import { SemesterService } from './semester.service';
import { JwtGuard } from 'src/auth/guard';
import { GetUser } from 'src/auth/decorator';
import { CreateSemesterTemplateDto } from './dto/semester-template.dto';
import { CreateSemesterDto } from './dto/create-semester.dto';
import { AssignTemplateDto } from './dto/assign-template.dto';

@Controller('semester')
export class SemesterController {
  private readonly logger = new Logger(SemesterController.name);

  constructor(private readonly semesterService: SemesterService) {}

  // Put static routes before parameterized routes
  @Get('templates')
  async getAllSemesterTemplates() {
    return this.semesterService.getAllSemesterTemplates();
  }

  @Get('list')  // Move this before any :id routes
  async getSemestersWithSubjects() {
    try {
      return await this.semesterService.getSemestersWithSubjects();
    } catch (error) {
      this.logger.error(`Error fetching semesters list: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch semesters list',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }


  @Post('template')
  async createSemesterTemplate(
    @Body() createSemesterTemplateDto: CreateSemesterTemplateDto
  ) {
    return this.semesterService.createSemesterTemplate(createSemesterTemplateDto);
  }

  @Get('template/:id')
  async getSemesterTemplateById(@Param('id', ParseIntPipe) id: number) {
    try {
      return await this.semesterService.getSemesterTemplateById(id);
    } catch (error) {
      this.logger.error(`Error fetching semester template: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch semester template',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('template/:id')
  async updateSemesterTemplate(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateData: Partial<CreateSemesterTemplateDto>
  ) {
    return this.semesterService.updateSemesterTemplate(id, updateData);
  }

  @Delete('template/:id')
  async deleteSemesterTemplate(@Param('id', ParseIntPipe) id: number) {
    return this.semesterService.deleteSemesterTemplate(id);
  }

  // Other semester routes
  @Get('current')
  async getCurrentSemester() {
    return this.semesterService.getCurrentSemester();
  }

  @UseGuards(JwtGuard)
  @Get('enrolled/numbers')
  async getEnrolledSemesters(@GetUser() user: any): Promise<Array<{ id: number; semesterNo: number }>> {
    return this.semesterService.getEnrolledSemesters(user.id);
  }

  @UseGuards(JwtGuard)
  @Get('enrolled')
  async getEnrolledSemestersDetails(@GetUser() user: any) {
    return this.semesterService.getEnrolledSemesters(user.id);
  }


  // Add new endpoint to assign template to semester
  @Put(':semesterId/assign-template')
  async assignTemplateToSemester(
    @Param('semesterId', ParseIntPipe) semesterId: number,
    @Body() assignTemplateDto: AssignTemplateDto,
  ) {
    return this.semesterService.assignTemplateToSemester(
      semesterId,
      assignTemplateDto.semesterTemplateId,
    );
  }

  @Post()
  async createSemester(@Body() createSemesterDto: CreateSemesterDto) {
    return this.semesterService.createSemester(createSemesterDto);
  }

  @Get('all')
  async getAllSemesters() {
    try {
      return await this.semesterService.getAllSemesters();
    } catch (error) {
      this.logger.error(`Error fetching all semesters: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch semesters',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  // Put parameterized routes last
  @Get(':id')
  async getSemesterById(@Param('id', ParseIntPipe) id: number) {
    return this.semesterService.getSemesterById(id);
  }

  @Put('add-user')
  async addUserToSemester(
    @Body()
    addUserToSemesterDto: {
      userId: number;
      semesterId: number;
      role: 'student' | 'teacher';
    },
  ) {
    if (!addUserToSemesterDto.userId || !addUserToSemesterDto.semesterId) {
      throw new HttpException(
        'userId and semesterId are required',
        HttpStatus.BAD_REQUEST
      );
    }

    if (!['student', 'teacher'].includes(addUserToSemesterDto.role)) {
      throw new HttpException(
        'role must be either "student" or "teacher"',
        HttpStatus.BAD_REQUEST
      );
    }

    const { userId, semesterId, role } = addUserToSemesterDto;
    return this.semesterService.addUserToSemester(userId, semesterId, role);
  }

  @Delete('remove-user')
  @HttpCode(HttpStatus.OK)
  async removeUserFromSemester(
    @Body()
    removeUserDto: {
      userId: number;
      semesterId: number;
      role: 'student' | 'teacher';
    },
  ) {
    if (!removeUserDto.userId || !removeUserDto.semesterId) {
      throw new HttpException(
        'userId and semesterId are required',
        HttpStatus.BAD_REQUEST
      );
    }

    if (!['student', 'teacher'].includes(removeUserDto.role)) {
      throw new HttpException(
        'role must be either "student" or "teacher"',
        HttpStatus.BAD_REQUEST
      );
    }

    const { userId, semesterId, role } = removeUserDto;
    return this.semesterService.removeUserFromSemester(userId, semesterId, role);
  }

  @Post('template/:templateId/add-subject')
  async addSubjectToTemplate(
    @Param('templateId') templateId: number,
    @Body()
    subjectData: {
      subjectId?: number;
      name?: string;
      code?: string;
      teacherId?: number;
    },
  ) {
    return this.semesterService.addSubjectToTemplate({
      templateId,
      ...subjectData,
    });
  }

  @Get(':semesterId/weeks')
  async getWeeksBySemester(@Param('semesterId', ParseIntPipe) semesterId: number) {
    return this.semesterService.getWeeksBySemester(semesterId);
  }

  @Post(':semesterId/week')
  async addWeekToSemester(
    @Param('semesterId', ParseIntPipe) semesterId: number,
    @Body() weekData: { weekNo: number; startDate: string; endDate: string }
  ) {
    try {
      const data = {
        weekNo: weekData.weekNo,
        startDate: new Date(weekData.startDate),
        endDate: new Date(weekData.endDate),
      };

      return await this.semesterService.addWeekToSemester(semesterId, data);
    } catch (error) {
      this.logger.error(`Error adding week to semester: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to add week to semester',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Put('week/:weekId')
  async updateWeek(
    @Param('weekId', ParseIntPipe) weekId: number,
    @Body() weekData: { weekNo?: number; startDate?: string; endDate?: string }
  ) {
    try {
      const data = {
        ...(weekData.weekNo && { weekNo: weekData.weekNo }),
        ...(weekData.startDate && { startDate: new Date(weekData.startDate) }),
        ...(weekData.endDate && { endDate: new Date(weekData.endDate) }),
      };

      return await this.semesterService.updateWeek(weekId, data);
    } catch (error) {
      this.logger.error(`Error updating week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to update week',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Post('week/:weekId/lesson')
  async addLessonToWeek(
    @Param('weekId', ParseIntPipe) weekId: number,
    @Body() lessonData: { lessonId: number }
  ) {
    return this.semesterService.addLessonToWeek(weekId, lessonData);
  }

  @Delete('week/:weekId/lesson/:lessonId')
  @HttpCode(HttpStatus.OK)
  async removeLessonFromWeek(
    @Param('weekId', ParseIntPipe) weekId: number,
    @Param('lessonId', ParseIntPipe) lessonId: number
  ) {
    return this.semesterService.removeLessonFromWeek(weekId, lessonId);
  }

  @Get('week/:weekId/lessons')
  async getLessonsByWeekId(@Param('weekId', ParseIntPipe) weekId: number) {
    try {
      return await this.semesterService.getLessonsByWeekId(weekId);
    } catch (error) {
      this.logger.error(`Error fetching lessons for week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch lessons',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
  @Get(':semesterId/weeks-list')
  async getSemesterWeeksList(
    @Param('semesterId', ParseIntPipe) semesterId: number
  ): Promise<Array<{ id: number; weekNo: number; startDate: Date; endDate: Date }>> {
    return this.semesterService.getSemesterWeeksList(semesterId);
  }
  @Get(':semesterId/subjects')
  async getSubjectsBySemesterId(@Param('semesterId', ParseIntPipe) semesterId: number) {
    try {
      return await this.semesterService.getSubjectsBySemesterId(semesterId);
    } catch (error) {
      this.logger.error(`Error fetching subjects for semester: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch subjects',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':semesterId/students')
  async getStudentsBySemesterId(@Param('semesterId', ParseIntPipe) semesterId: number) {
    try {
      return await this.semesterService.getStudentsBySemesterId(semesterId);
    } catch (error) {
      this.logger.error(`Error fetching students for semester: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch students',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(JwtGuard)
  @Get('week/:weekId/subjects')
  async getSubjectsAndLessonsByWeekId(
    @Param('weekId', ParseIntPipe) weekId: number,
    @GetUser('id') userId: number
  ) {
    try {
      // Get the student ID from the user ID
      const student = await this.semesterService['prisma'].student.findUnique({
        where: { userId },
        select: { id: true }
      });

      // Pass the student ID to the service method if the user is a student
      const studentId = student ? student.id : undefined;

      return await this.semesterService.getSubjectsAndLessonsByWeekId(weekId, studentId);
    } catch (error) {
      this.logger.error(`Error fetching subjects and lessons for week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch subjects and lessons',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
