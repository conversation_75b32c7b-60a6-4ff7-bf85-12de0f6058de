# Optional timeLimit Field Examples

## Overview

The `timeLimit` field in quiz DTOs is now optional. This allows creating quizzes without time constraints or updating quizzes to remove time limits.

## Create Quiz Examples

### Example 1: Quiz with Time Limit

```json
POST /quiz

{
  "name": "Timed Music Theory Quiz",
  "subjectId": 1,
  "lessonId": 2,
  "grade": 10,
  "type": "Week",
  "numberOfAttempts": 2,
  "timeLimit": 30,
  "isRecord": false,
  "content": [
    {
      "type": "mcq",
      "question": "What is a major scale?",
      "grade": 5,
      "answers": [
        {"id": 0, "text": "A scale with 7 notes"},
        {"id": 1, "text": "A scale following W-W-H-W-W-W-H pattern"},
        {"id": 2, "text": "A scale in high pitch"},
        {"id": 3, "text": "A scale with sharps only"}
      ],
      "correctAnswerId": 1
    },
    {
      "type": "text",
      "question": "Name three Baroque composers",
      "grade": 5,
      "correctAnswer": "<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>"
    }
  ]
}
```

### Example 2: Quiz without Time Limit

```json
POST /quiz

{
  "name": "Untimed Research Assignment",
  "subjectId": 1,
  "lessonId": 2,
  "grade": 20,
  "type": "Final",
  "numberOfAttempts": 1,
  "isRecord": false,
  "content": [
    {
      "type": "text",
      "question": "Write a comprehensive essay about the development of church music from the medieval period to the Renaissance. Include specific examples of composers, musical forms, and their historical significance.",
      "grade": 20,
      "correctAnswer": "Expected to cover Gregorian chant, polyphony development, composers like Palestrina, Josquin, etc."
    }
  ]
}
```

### Example 3: Recording Quiz without Time Limit

```json
POST /quiz

{
  "name": "Oral Performance Assessment",
  "subjectId": 1,
  "lessonId": 3,
  "grade": 15,
  "type": "Final",
  "numberOfAttempts": 1,
  "isRecord": true,
  "content": [
    {
      "type": "record",
      "question": "Record yourself performing the hymn 'How Great Thou Art' with proper vocal technique and expression",
      "grade": 10,
      "maxDuration": 600
    },
    {
      "type": "text",
      "question": "Explain your interpretation choices and the theological significance of this hymn",
      "grade": 5,
      "correctAnswer": "Should discuss musical phrasing, dynamics, tempo choices, and theological themes"
    }
  ]
}
```

## Update Quiz Examples

### Example 1: Add Time Limit to Existing Quiz

```json
PUT /quiz/5

{
  "timeLimit": 45
}
```

### Example 2: Remove Time Limit from Existing Quiz

```json
PUT /quiz/5

{
  "timeLimit": null
}
```

### Example 3: Update Multiple Fields Including Optional timeLimit

```json
PUT /quiz/5

{
  "name": "Updated Quiz Name",
  "numberOfAttempts": 3,
  "timeLimit": 60,
  "isRecord": true
}
```

## Response Examples

### Quiz with Time Limit Response

```json
{
  "message": "Quiz created successfully",
  "data": {
    "id": 10,
    "name": "Timed Music Theory Quiz",
    "subjectId": 1,
    "grade": 10,
    "createdAt": "2025-05-22T20:30:00.000Z",
    "updatedAt": "2025-05-22T20:30:00.000Z",
    "type": "Week",
    "numberOfAttempts": 2,
    "timeLimit": 30,
    "lessonId": 2,
    "isRecord": false,
    "subject": {
      "id": 1,
      "name": "الحان",
      "code": "MUS101"
    },
    "lesson": {
      "id": 2,
      "name": "Music Theory Basics"
    }
  }
}
```

### Quiz without Time Limit Response

```json
{
  "message": "Quiz created successfully",
  "data": {
    "id": 11,
    "name": "Untimed Research Assignment",
    "subjectId": 1,
    "grade": 20,
    "createdAt": "2025-05-22T20:35:00.000Z",
    "updatedAt": "2025-05-22T20:35:00.000Z",
    "type": "Final",
    "numberOfAttempts": 1,
    "timeLimit": null,
    "lessonId": 2,
    "isRecord": false,
    "subject": {
      "id": 1,
      "name": "الحان",
      "code": "MUS101"
    },
    "lesson": {
      "id": 2,
      "name": "Music Theory Basics"
    }
  }
}
```

## Use Cases

### Timed Quizzes (timeLimit specified)
- **Quick assessments**: Short quizzes with 10-15 minute limits
- **Exam simulations**: Longer quizzes with 60-90 minute limits
- **Speed tests**: Knowledge recall under time pressure
- **Standardized assessments**: Consistent timing across all students

### Untimed Quizzes (timeLimit not specified or null)
- **Research assignments**: Students need time to research and formulate answers
- **Essay questions**: Complex written responses requiring reflection
- **Creative projects**: Open-ended assignments with flexible completion time
- **Accessibility accommodations**: Students with special needs requiring extra time
- **Take-home exams**: Assignments completed over several days
- **Recording assessments**: Performance-based evaluations where quality matters more than speed

## Frontend Implementation Notes

1. **Conditional Timer**: Only show countdown timer when `timeLimit` is not null
2. **UI Indicators**: Display "Untimed" or "No Time Limit" when timeLimit is null
3. **Validation**: Allow form submission without timeLimit value
4. **Auto-save**: For untimed quizzes, implement periodic auto-save functionality
5. **Warning Messages**: Inform students whether quiz is timed or untimed before starting

## Validation Rules

- **timeLimit**: Optional integer, must be positive if provided
- **Database**: Stores null when not specified
- **API**: Accepts requests with or without timeLimit field
- **Updates**: Can set timeLimit to null to remove time constraints

## Backward Compatibility

- Existing quizzes with timeLimit values remain unchanged
- New quizzes can be created with or without timeLimit
- Updates can add, modify, or remove timeLimit values
- All quiz retrieval endpoints handle both timed and untimed quizzes
