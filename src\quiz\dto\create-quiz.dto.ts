import { Type } from 'class-transformer';
import { IsString, IsInt, IsNotEmpty, IsArray, ValidateNested, IsEnum, IsNumber, IsOptional, Min, IsBoolean } from 'class-validator';
import { SubCategory } from '../../subject/dto/subcategory.dto';

export enum QuestionType {
  MCQ = 'mcq',
  TEXT = 'text',
  RECORD = 'record'
}

class MCQAnswer {
  @IsString()
  @IsNotEmpty()
  text: string;

  @IsNumber()
  id: number;
}

class BaseQuestion {
  @IsString()
  @IsNotEmpty()
  question: string;

  @IsEnum(QuestionType, {
    message: `type must be one of the following values: ${Object.values(QuestionType).join(', ')}`
  })
  type: QuestionType;

  @IsNumber()
  @Min(0)
  grade: number;
}

class MCQQuestion extends BaseQuestion {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => MCQAnswer)
  answers: MCQAnswer[];

  @IsNumber()
  correctAnswerId: number;
}

class TextQuestion extends BaseQuestion {
  @IsString()
  @IsOptional()
  correctAnswer?: string;
}

class RecordQuestion extends BaseQuestion {
  @IsNumber()
  @IsOptional()
  maxDuration?: number;
}

export class CreateQuizDto {
  @IsString()
  @IsNotEmpty()
  name: string;

  @IsInt()
  subjectId: number;


  @IsInt()
  @IsOptional()
  lessonId?: number;

  @IsInt()
  grade: number;

  @IsString()
  type: string;

  @IsInt()
  numberOfAttempts: number;

  @IsInt()
  @IsOptional()
  timeLimit?: number;

  @IsBoolean()
  @IsOptional()
  isRecord?: boolean; // Optional flag to indicate if this quiz supports recording

  @IsOptional()
  @ValidateNested()
  @Type(() => SubCategory)
  subCategory?: SubCategory;

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => BaseQuestion, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'type',
      subTypes: [
        { value: MCQQuestion, name: QuestionType.MCQ },
        { value: TextQuestion, name: QuestionType.TEXT },
        { value: RecordQuestion, name: QuestionType.RECORD },
      ],
    },
  })
  content: (MCQQuestion | TextQuestion | RecordQuestion)[];
}




