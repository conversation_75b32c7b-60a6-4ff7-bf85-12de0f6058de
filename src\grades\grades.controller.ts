import { <PERSON>, Get, HttpException, HttpStatus, Logger, Param, ParseIntPipe, UseGuards } from '@nestjs/common';
import { GradesService } from './grades.service';
import { GetUser } from 'src/auth/decorator';
import { JwtGuard } from 'src/auth/guard';

@Controller('grades')
export class GradesController {
  private readonly logger = new Logger(GradesController.name);

  constructor(private readonly gradesService: GradesService) {}

  @Get('semester/:semesterId/student/:studentId')
  async getGradesBySemesterAndStudent(
    @Param('semesterId', ParseIntPipe) semesterId: number,
    @Param('studentId', ParseIntPipe) studentId: number,
  ) {
    try {
      this.logger.debug(`Fetching grades for semester ${semesterId} and student ${studentId}`);
      return await this.gradesService.getGradesBySemesterAndStudent(semesterId, studentId);
    } catch (error) {
      this.logger.error(`Error fetching grades: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch grades',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @UseGuards(JwtGuard)
  @Get('student/summary')
  async getSummaryGradesByStudent(@GetUser('id') userId: number) {
    try {
      this.logger.debug(`Fetching summary grades for user ${userId}`);
      return await this.gradesService.getSummaryGradesByStudent(userId);
    } catch (error) {
      this.logger.error(`Error fetching summary grades: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch summary grades',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

}
