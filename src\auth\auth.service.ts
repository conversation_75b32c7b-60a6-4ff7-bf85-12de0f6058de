import { ForbiddenException, Injectable, BadRequestException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { LoginDto, RegisterDto, UserRole } from './dto/auth.dto';
import * as argon from 'argon2';
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library';
import { JwtService } from '@nestjs/jwt';
import { ConfigService } from '@nestjs/config';
@Injectable()
export class AuthService {
  constructor(
    private prisma: PrismaService,
    private jwt: JwtService,
    private config: ConfigService,
  ) {}

  async register(registerDto: RegisterDto) {
    const hashedPassword = await argon.hash(registerDto.password);

    try {
      // First create the user
      const user = await this.prisma.user.create({
        data: {
          email: registerDto.email,
          password: hashedPassword,
          phone: registerDto.phone,
          name: registerDto.name,
          birthday: new Date(registerDto.birthday),
          nationality: registerDto.nationality,
          Address: registerDto.Address,
          gender: registerDto.gender,
          profilePicture: registerDto.profilePicture,
        },
      });

      // Based on role, create either student or teacher
      if (registerDto.role === UserRole.STUDENT) {
        if (!registerDto.city || !registerDto.church || !registerDto.AbEle3traf ||
            !registerDto.deaconLevel || !registerDto.churchService) {
          throw new BadRequestException('Missing required student fields');
        }

        await this.prisma.student.create({
          data: {
            userId: user.id,
            studentCode: `STU${user.id}`, // Generate student code
            city: registerDto.city,
            church: registerDto.church,
            AbEle3traf: registerDto.AbEle3traf,
            deaconLevel: registerDto.deaconLevel,
            churchService: registerDto.churchService,
            qualifications: registerDto.qualifications,
            personalIDFront: registerDto.personalIDFront,
            personalIDBack: registerDto.personalIDBack,
            Tazkia: registerDto.Tazkia,
          },
        });
      } else if (registerDto.role === UserRole.TEACHER) {
        await this.prisma.teacher.create({
          data: {
            userId: user.id,
            teacherCode: `TCH${user.id}`, // Generate teacher code
            qualifications: registerDto.qualifications,
            personalIDFront: registerDto.personalIDFront,
            personalIDBack: registerDto.personalIDBack,
            Tazkia: registerDto.Tazkia,
          },
        });
      }

      const tokens = await this.getTokens(user.id, user.email);
      await this.updateRtHash(user.id, tokens.refresh_token);
      return tokens;
    } catch (err) {
      if (err instanceof PrismaClientKnownRequestError) {
        if (err.code === 'P2002') {
          throw new ForbiddenException('Email already exists');
        }
      }
      throw err;
    }
  }

  async login(loginDto: LoginDto) {
    const user = await this.prisma.user.findUnique({
      where: { email: loginDto.email },
    });
    if (!user) throw new ForbiddenException('Invalid credentials');

    const passwordMatches = await argon.verify(user.password, loginDto.password);
    if (!passwordMatches) throw new ForbiddenException('Invalid credentials');

    const tokens = await this.getTokens(user.id, user.email);
    await this.updateRtHash(user.id, tokens.refresh_token);
    return tokens;
  }

  async logout(userId: number) {
    await this.prisma.user.updateMany({
      where: {
        id: userId,
        hashedRt: { not: null },
      },
      data: {
        hashedRt: null,
      },
    });
  }

  async refreshTokens(userId: number, rt: string) {
    const user = await this.prisma.user.findUnique({
      where: { id: userId },
    });
    if (!user || !user.hashedRt) throw new ForbiddenException('Access Denied');

    const rtMatches = await argon.verify(user.hashedRt, rt);
    if (!rtMatches) throw new ForbiddenException('Access Denied');

    const tokens = await this.getTokens(user.id, user.email);
    await this.updateRtHash(user.id, tokens.refresh_token);
    return tokens;
  }

  private async updateRtHash(userId: number, rt: string) {
    const hash = await argon.hash(rt);
    await this.prisma.user.update({
      where: { id: userId },
      data: { hashedRt: hash },
    });
  }

  private async getTokens(userId: number, email: string) {
    const [at, rt] = await Promise.all([
      this.jwt.signAsync(
        { sub: userId, email },
        {
          secret: this.config.get('JWT_SECRET'),
          expiresIn: '2d',
        },
      ),
      this.jwt.signAsync(
        { sub: userId, email },
        {
          secret: this.config.get('JWT_RT_SECRET'),
          expiresIn: '7d',
        },
      ),
    ]);

    return {
      access_token: at,
      refresh_token: rt,
    };
  }
}
