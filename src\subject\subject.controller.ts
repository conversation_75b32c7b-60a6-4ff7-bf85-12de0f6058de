import { Body, Controller, Get, Param, ParseIntPipe, Post, Delete, HttpCode, HttpStatus } from '@nestjs/common';
import { SubjectService } from './subject.service';
import { CreateSubjectDto } from './dto/subject.dto';
import { CreateLessonDto } from './dto/create-lesson.dto';
import { CreateItemDto } from './dto/create-item.dto';
import { AddSubCategoryDto } from './dto/subcategory.dto';

@Controller('subject')
export class SubjectController {
  constructor(private readonly subjectService: SubjectService) {}

  @Post()
  async createSubject(@Body() createSubjectDto: CreateSubjectDto) {
    return this.subjectService.createSubject(createSubjectDto);
  }

  @Get('template/:templateId')
  async getSubjectsByTemplateId(
    @Param('templateId', ParseIntPipe) templateId: number,
  ) {
    return this.subjectService.getSubjectsByTemplateId(templateId);
  }

  @Post('lesson')
  async createLesson(@Body() createLessonDto: CreateLessonDto) {
    return this.subjectService.createLesson(createLessonDto);
  }

  @Post('lesson/item')
  async createItem(@Body() createItemDto: CreateItemDto) {
    return this.subjectService.createItem(createItemDto);
  }

  @Get(':subjectId/lessons')
  async getLessonsBySubjectId(
    @Param('subjectId', ParseIntPipe) subjectId: number,
  ) {
    return this.subjectService.getLessonsBySubjectId(subjectId);
  }

  @Get('lesson/:lessonId/items')
  async getItemsByLessonId(
    @Param('lessonId', ParseIntPipe) lessonId: number,
  ) {
    return this.subjectService.getItemsByLessonId(lessonId);
  }

  @Delete('lesson/item/:itemId')
  @HttpCode(HttpStatus.OK)
  async deleteItem(@Param('itemId', ParseIntPipe) itemId: number) {
    return this.subjectService.deleteItem(itemId);
  }

  @Post(':subjectId/subcategory')
  async addSubCategoryToSubject(
    @Param('subjectId', ParseIntPipe) subjectId: number,
    @Body() addSubCategoryDto: AddSubCategoryDto,
  ) {
    return this.subjectService.addSubCategoryToSubject(subjectId, addSubCategoryDto);
  }

  @Delete(':subjectId/subcategory/:subCategoryId')
  @HttpCode(HttpStatus.OK)
  async deleteSubCategoryFromSubject(
    @Param('subjectId', ParseIntPipe) subjectId: number,
    @Param('subCategoryId', ParseIntPipe) subCategoryId: number,
  ) {
    return this.subjectService.deleteSubCategoryFromSubject(subjectId, subCategoryId);
  }

  @Get(':subjectId/subcategories')
  async getSubCategoriesBySubjectId(@Param('subjectId', ParseIntPipe) subjectId: number) {
    return this.subjectService.getSubCategoriesBySubjectId(subjectId);
  }
}


