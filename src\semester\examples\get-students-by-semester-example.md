# Get Students by Semester ID API Example

## Get All Students in a Semester

This endpoint retrieves all students enrolled in a specific semester, returning only their ID, name, and student code.

### Endpoint

```
GET /semester/:semesterId/students
```

### Path Parameters

- `semesterId` (number): The ID of the semester to get students for

### Example Request

```
GET /semester/1/students
```

### Example Response (Success)

```json
{
  "message": "Students retrieved successfully",
  "data": {
    "semester": {
      "id": 1,
      "name": "Spring 2025"
    },
    "students": [
      {
        "id": 5,
        "name": "<PERSON>",
        "code": "STU5"
      },
      {
        "id": 7,
        "name": "<PERSON>",
        "code": "STU7"
      },
      {
        "id": 12,
        "name": "<PERSON>",
        "code": "STU12"
      },
      {
        "id": 15,
        "name": "<PERSON>",
        "code": "STU15"
      }
    ]
  }
}
```

### Example Response (Empty Semester)

```json
{
  "message": "Students retrieved successfully",
  "data": {
    "semester": {
      "id": 2,
      "name": "Fall 2025"
    },
    "students": []
  }
}
```

### Example Response (Error - Semester Not Found)

```json
{
  "message": "Semester with ID 999 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

## Response Structure

### Success Response

- **message**: Success message
- **data**: Object containing:
  - **semester**: Object with semester information
    - **id**: Semester ID
    - **name**: Semester name
  - **students**: Array of student objects, each containing:
    - **id**: Student ID (used for internal operations)
    - **name**: Student's full name
    - **code**: Student code (e.g., "STU5")

## Use Cases

1. **Administrative Dashboard**: Display all students enrolled in a specific semester
2. **Redo Entry Management**: Get student IDs and names for creating redo entries
3. **Grade Management**: List students for grading purposes
4. **Attendance Tracking**: Get student list for attendance management
5. **Communication**: Get student information for sending notifications or announcements

## Notes

- Students are ordered by their student code in ascending order
- Only basic student information is returned (ID, name, code) for performance and privacy
- The endpoint validates that the semester exists before retrieving students
- Returns an empty array if no students are enrolled in the semester
- Uses the many-to-many relationship between Student and Semester models

## Related Endpoints

- `GET /semester/:semesterId/subjects` - Get subjects for a semester
- `GET /semester/:semesterId/weeks` - Get weeks for a semester
- `POST /quiz-answers/redo` - Add redo entry (uses student ID from this endpoint)
- `GET /semester/all` - Get all semesters
