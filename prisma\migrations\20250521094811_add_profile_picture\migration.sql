-- AlterTable
ALTER TABLE "quiz" ADD COLUMN     "lessonId" INTEGER;

-- CreateTable
CREATE TABLE "Announcement" (
    "id" SERIAL NOT NULL,
    "title" TEXT NOT NULL,
    "description" TEXT NOT NULL,
    "meetingLink" TEXT,
    "imageUrl" TEXT,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "Announcement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "WeekAnnouncement" (
    "id" SERIAL NOT NULL,
    "announcementId" INTEGER NOT NULL,
    "weekId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "WeekAnnouncement_pkey" PRIMARY KEY ("id")
);

-- CreateTable
CREATE TABLE "quizAnswer" (
    "id" SERIAL NOT NULL,
    "studentId" INTEGER NOT NULL,
    "quizId" INTEGER NOT NULL,
    "answers" JSONB NOT NULL,
    "grade" INTEGER,
    "timeTaken" INTEGER,
    "autoGraded" BOOLEAN NOT NULL DEFAULT false,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "quizAnswer_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "WeekAnnouncement_announcementId_idx" ON "WeekAnnouncement"("announcementId");

-- CreateIndex
CREATE INDEX "WeekAnnouncement_weekId_idx" ON "WeekAnnouncement"("weekId");

-- CreateIndex
CREATE INDEX "quizAnswer_studentId_idx" ON "quizAnswer"("studentId");

-- CreateIndex
CREATE INDEX "quizAnswer_quizId_idx" ON "quizAnswer"("quizId");

-- AddForeignKey
ALTER TABLE "quiz" ADD CONSTRAINT "quiz_lessonId_fkey" FOREIGN KEY ("lessonId") REFERENCES "Lesson"("id") ON DELETE SET NULL ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WeekAnnouncement" ADD CONSTRAINT "WeekAnnouncement_announcementId_fkey" FOREIGN KEY ("announcementId") REFERENCES "Announcement"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "WeekAnnouncement" ADD CONSTRAINT "WeekAnnouncement_weekId_fkey" FOREIGN KEY ("weekId") REFERENCES "Week"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quizAnswer" ADD CONSTRAINT "quizAnswer_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- AddForeignKey
ALTER TABLE "quizAnswer" ADD CONSTRAINT "quizAnswer_quizId_fkey" FOREIGN KEY ("quizId") REFERENCES "quiz"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
