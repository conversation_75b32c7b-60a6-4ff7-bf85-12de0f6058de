import { Injectable, Logger, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';

@Injectable()
export class GradesService {
  private readonly logger = new Logger(GradesService.name);

  constructor(private prisma: PrismaService) {}

  async getGradesBySemesterAndStudent(semesterId: number, studentId: number) {
    try {
      // Check if semester exists
      const semester = await this.prisma.semester.findUnique({
        where: { id: semesterId },
        select: {
          id: true,
          name: true,
          semesterTemplate: {
            select: {
              Subjects: {
                select: {
                  id: true,
                  name: true,
                  code: true,
                }
              }
            }
          }
        },
      });

      if (!semester) {
        throw new NotFoundException(`Semester with ID ${semesterId} not found`);
      }

      // Check if student exists
      const student = await this.prisma.student.findUnique({
        where: { id: studentId },
        select: {
          id: true,
          studentCode: true,
          user: {
            select: {
              name: true,
            }
          }
        },
      });

      if (!student) {
        throw new NotFoundException(`Student with ID ${studentId} not found`);
      }

      // Get all quizzes for subjects in this semester
      const subjectIds = semester.semesterTemplate.Subjects.map(subject => subject.id);

      const quizzes = await this.prisma.quiz.findMany({
        where: {
          subjectId: {
            in: subjectIds
          }
        },
        select: {
          id: true,
          name: true,
          grade: true,
          type: true,
          subjectId: true,
          subCategory: true,
        },
        orderBy: {
          createdAt: 'asc'
        }
      });

      // Get all quiz answers for this student for these quizzes
      const quizIds = quizzes.map(quiz => quiz.id);
      const quizAnswers = await this.prisma.quizAnswer.findMany({
        where: {
          studentId: studentId,
          quizId: {
            in: quizIds
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Group quiz answers by quiz ID (latest first)
      const latestQuizAnswers = quizAnswers.reduce((acc, answer) => {
        if (!acc[answer.quizId]) {
          acc[answer.quizId] = answer;
        }
        return acc;
      }, {});

      // Group quizzes by subject
      const subjectGrades = semester.semesterTemplate.Subjects.map(subject => {
        const subjectQuizzes = quizzes.filter(quiz => quiz.subjectId === subject.id);

        // Separate final and week quizzes
        const finalQuizzes = subjectQuizzes.filter(quiz => quiz.type === 'Final');
        const weekQuizzes = subjectQuizzes.filter(quiz => quiz.type === 'Week');

        // Process final quizzes
        const finalQuizGrades = finalQuizzes.map(quiz => {
          const latestAnswer = latestQuizAnswers[quiz.id];
          return {
            name: quiz.name,
            userGrade: latestAnswer ? latestAnswer.grade : null,
            finalGrade: quiz.grade,
            subCategory: quiz.subCategory ? JSON.parse(quiz.subCategory as string) : null
          };
        });

        // Process week quizzes - aggregate them
        let weekQuizGrades = null;
        if (weekQuizzes.length > 0) {
          const totalWeekFinalGrade = weekQuizzes.reduce((sum, quiz) => sum + quiz.grade, 0);

          // For week quizzes, replace null grades with 0 and sum them up
          const totalWeekUserGrade = weekQuizzes.reduce((sum, quiz) => {
            const latestAnswer = latestQuizAnswers[quiz.id];
            const grade = latestAnswer && latestAnswer.grade !== null ? latestAnswer.grade : 0;
            return sum + grade;
          }, 0);

          // Check if all week quizzes are null (no attempts or all pending)
          const allWeekQuizzesNull = weekQuizzes.every(quiz => {
            const latestAnswer = latestQuizAnswers[quiz.id];
            return !latestAnswer || latestAnswer.grade === null;
          });

          weekQuizGrades = {
            name: 'Week Quizzes',
            userGrade: allWeekQuizzesNull ? null : totalWeekUserGrade,
            finalGrade: totalWeekFinalGrade
          };
        }

        // Combine final quizzes and week quiz aggregate
        const allQuizzes = [...finalQuizGrades];
        if (weekQuizGrades) {
          allQuizzes.push(weekQuizGrades);
        }

        return {
          id: subject.id,
          name: subject.name,
          code: subject.code,
          quizzes: allQuizzes
        };
      });

      return {
        message: 'Grades retrieved successfully',
        data: {
          semester: {
            id: semester.id,
            name: semester.name,
          },
          student: {
            id: student.id,
            name: student.user.name,
            code: student.studentCode,
          },
          subjects: subjectGrades
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get grades for semester ${semesterId} and student ${studentId}: ${error.message}`);
      throw error;
    }
  }

  async getSummaryGradesByStudent(userId: number) {
    try {
      // Get student by user ID
      const student = await this.prisma.student.findUnique({
        where: { userId: userId },
        select: {
          id: true,
          studentCode: true,
          user: {
            select: {
              name: true,
            }
          },
          semester: {
            select: {
              id: true,
              name: true,
              semesterTemplate: {
                select: {
                  semesterNo: true,
                  Subjects: {
                    select: {
                      id: true,
                      name: true,
                      code: true,
                    }
                  }
                }
              }
            }
          }
        },
      });

      if (!student) {
        throw new NotFoundException(`User is not a student or student not found`);
      }

      // Get all semesters the student is enrolled in
      const enrolledSemesters = student.semester;

      if (enrolledSemesters.length === 0) {
        return {
          message: 'Student summary grades retrieved successfully',
          data: {
            student: {
              id: student.id,
              name: student.user.name,
              code: student.studentCode,
            },
            semesters: []
          }
        };
      }

      // Process each semester
      const semesterSummaries = await Promise.all(
        enrolledSemesters.map(async (semester) => {
          const subjects = semester.semesterTemplate.Subjects;

          // Get all quizzes for subjects in this semester
          const subjectIds = subjects.map(subject => subject.id);
          const quizzes = await this.prisma.quiz.findMany({
            where: {
              subjectId: {
                in: subjectIds
              }
            },
            select: {
              id: true,
              name: true,
              grade: true,
              type: true,
              subjectId: true,
              lessonId: true,
            },
            orderBy: {
              createdAt: 'asc'
            }
          });

          // Get all quiz answers for this student for these quizzes
          const quizIds = quizzes.map(quiz => quiz.id);
          const quizAnswers = await this.prisma.quizAnswer.findMany({
            where: {
              studentId: student.id,
              quizId: {
                in: quizIds
              }
            },
            orderBy: {
              createdAt: 'desc'
            }
          });

          // Create a map of latest quiz answers by quiz ID
          const latestQuizAnswers: { [quizId: number]: any } = {};
          quizAnswers.forEach(answer => {
            if (!latestQuizAnswers[answer.quizId]) {
              latestQuizAnswers[answer.quizId] = answer;
            }
          });

          // Process each subject
          const subjectSummaries = subjects.map(subject => {
            const subjectQuizzes = quizzes.filter(quiz => quiz.subjectId === subject.id);

            // Separate final and week quizzes
            const finalQuizzes = subjectQuizzes.filter(quiz => quiz.type === 'Final');
            const weekQuizzes = subjectQuizzes.filter(quiz => quiz.type === 'Week');

            // Calculate final quiz totals
            let finalQuizTotalScore = 0;
            let finalQuizFinalScore = 0;

            finalQuizzes.forEach(quiz => {
              const answer = latestQuizAnswers[quiz.id];
              finalQuizTotalScore += answer?.grade || 0;
              finalQuizFinalScore += quiz.grade;
            });

            // Calculate week quiz totals
            let weekQuizTotalScore = 0;
            let weekQuizFinalScore = 0;

            weekQuizzes.forEach(quiz => {
              const answer = latestQuizAnswers[quiz.id];
              weekQuizTotalScore += answer?.grade || 0;
              weekQuizFinalScore += quiz.grade;
            });

            return {
              id: subject.id,
              name: subject.name,
              code: subject.code,
              finalQuizzes: {
                totalScore: finalQuizTotalScore,
                finalScore: finalQuizFinalScore,
                hasQuizzes: finalQuizzes.length > 0
              },
              weekQuizzes: {
                totalScore: weekQuizTotalScore,
                finalScore: weekQuizFinalScore,
                hasQuizzes: weekQuizzes.length > 0
              }
            };
          });

          return {
            id: semester.id,
            name: semester.name,
            semesterNo: semester.semesterTemplate.semesterNo,
            subjects: subjectSummaries
          };
        })
      );

      return {
        message: 'Student summary grades retrieved successfully',
        data: {
          student: {
            id: student.id,
            name: student.user.name,
            code: student.studentCode,
          },
          semesters: semesterSummaries
        }
      };
    } catch (error) {
      this.logger.error(`Failed to get summary grades for user ${userId}: ${error.message}`);
      throw error;
    }
  }
}
