# Grades Module

The Grades module provides functionality for retrieving and managing student grades across different semesters and subjects.

## Features

- **Grade Retrieval**: Get comprehensive grade information for students by semester
- **Quiz Aggregation**: Automatically aggregates week quizzes while keeping final quizzes separate
- **Latest Submission**: Always uses the most recent quiz submission for grade calculation
- **Subject Organization**: Groups grades by subject for better organization

## Endpoints

### GET /grades/semester/:semesterId/student/:studentId

Retrieves all grades for a specific student in a specific semester.

**Parameters:**
- `semesterId`: The ID of the semester
- `studentId`: The ID of the student

**Response Structure:**
```json
{
  "message": "Grades retrieved successfully",
  "data": {
    "semester": { 
      "id": 1, 
      "name": "Spring 2025",
      "number": 1 
    },
    "student": { "id": 5, "name": "<PERSON>", "code": "STU5" },
    "subjects": [
      {
        "id": 1,
        "name": "Subject Name",
        "code": "SUB101",
        "quizzes": [
          {
            "name": "Quiz Name",
            "userGrade": 85,
            "finalGrade": 100
          }
        ]
      }
    ]
  }
}
```

## Grade Aggregation Logic

### Final Quizzes (type: "Final")
- Displayed individually with their specific names
- Shows the student's grade from their latest submission
- If no submission exists or grade is null, userGrade is null

### Week Quizzes (type: "Week")
- All week quizzes for a subject are aggregated into a single "Week Quizzes" entry
- finalGrade: Sum of all week quiz maximum grades
- userGrade: Sum of all week quiz grades, where null grades are replaced with 0
- Only shows null if ALL week quizzes have no attempts or are all pending grading

## Dependencies

- **PrismaService**: For database operations
- **NestJS Common**: For decorators and HTTP handling

## Error Handling

- **404 Not Found**: When semester or student doesn't exist
- **500 Internal Server Error**: For unexpected errors during processing

## Usage Examples

See the [examples directory](./examples/) for detailed request and response examples.

## Module Structure

```
src/grades/
├── grades.module.ts       # Module definition
├── grades.controller.ts   # HTTP endpoints
├── grades.service.ts      # Business logic
├── examples/             # Usage examples
│   └── get-grades-example.md
└── README.md             # This file
```
