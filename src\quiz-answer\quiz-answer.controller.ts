import { Body, Controller, Get, HttpException, HttpStatus, Logger, Param, ParseIntPipe, Post, Put, Query, UseGuards } from '@nestjs/common';
import { QuizAnswerService } from './quiz-answer.service';
import { CreateQuizAnswerDto, GradeQuizAnswerDto } from './dto';
import { AddRedoDto } from './dto/add-redo.dto';
import { GetUser } from 'src/auth/decorator';
import { JwtGuard } from 'src/auth/guard';

@Controller('quiz-answers')
export class QuizAnswerController {
  private readonly logger = new Logger(QuizAnswerController.name);

  constructor(private readonly quizAnswerService: QuizAnswerService) {}

  @UseGuards(JwtGuard)
  @Post()
  async submitQuizAnswer(
    @Body() createQuizAnswerDto: CreateQuizAnswerDto,
    @GetUser('id') userId: number,
  ) {
    try {
      this.logger.debug(`Submitting quiz answer for quiz ID: ${createQuizAnswerDto.quizId}`);

      // Get the student ID from the user ID
      const student = await this.quizAnswerService['prisma'].student.findUnique({
        where: { userId },
        select: {
          id: true,
          studentCode: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      if (!student) {
        throw new HttpException('User is not a student', HttpStatus.FORBIDDEN);
      }

      // Set the studentId in the DTO
      createQuizAnswerDto.studentId = student.id;

      // Get the quiz details to fetch required fields
      const quiz = await this.quizAnswerService['prisma'].quiz.findUnique({
        where: { id: createQuizAnswerDto.quizId },
        select: {
          id: true,
          subjectId: true,
          lessonId: true,
        },
      });

      if (!quiz) {
        throw new HttpException(`Quiz with ID ${createQuizAnswerDto.quizId} not found`, HttpStatus.NOT_FOUND);
      }

      // Set additional fields in the DTO
      createQuizAnswerDto.subjectId = quiz.subjectId;
      createQuizAnswerDto.lessonId = quiz.lessonId || 0; // Fallback to 0 if not available

      // Get the current semester
      const currentSemester = await this.quizAnswerService['prisma'].semester.findFirst({
        where: { isCurrent: true },
        select: { id: true },
      });

      if (!currentSemester) {
        throw new HttpException('No active semester found', HttpStatus.BAD_REQUEST);
      }

      createQuizAnswerDto.semesterId = currentSemester.id;

      return await this.quizAnswerService.submitQuizAnswer(createQuizAnswerDto);
    } catch (error) {
      this.logger.error(`Error submitting quiz answer: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to submit quiz answer',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }


  @UseGuards(JwtGuard)
  @Get('user/answers-with-questions')
  async getUserAnswersWithQuestions(@GetUser('id') userId: number) {
    try {
      this.logger.debug(`Fetching quiz answers with questions for user ID: ${userId}`);
      return await this.quizAnswerService.getUserAnswersWithQuestions(userId);
    } catch (error) {
      this.logger.error(`Error fetching user answers with questions: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch user answers with questions',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id/grade')
  async gradeQuizAnswer(
    @Param('id', ParseIntPipe) id: number,
    @Body() gradeQuizAnswerDto: GradeQuizAnswerDto,
  ) {
    try {
      this.logger.debug(`Grading quiz answer with ID: ${id}, grade: ${gradeQuizAnswerDto.grade}`);
      return await this.quizAnswerService.gradeQuizAnswer(id, gradeQuizAnswerDto);
    } catch (error) {
      this.logger.error(`Error grading quiz answer: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to grade quiz answer',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('quiz/:quizId')
  async getQuizAnswersByQuizId(@Param('quizId', ParseIntPipe) quizId: number) {
    try {
      this.logger.debug(`Fetching quiz answers for quiz ID: ${quizId}`);
      return await this.quizAnswerService.getQuizAnswersByQuizId(quizId);
    } catch (error) {
      this.logger.error(`Error fetching quiz answers: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quiz answers',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('summary')
  async getQuizAnswers(
    @Query('quizId') quizId?: string,
    @Query('lessonId') lessonId?: string,
    @Query('subjectId') subjectId?: string,
    @Query('weekId') weekId?: string,
    @Query('semesterId') semesterId?: string,
  ) {
    try {
      this.logger.debug(`Fetching quiz answers summary with filters:
        quizId: ${quizId},
        lessonId: ${lessonId},
        subjectId: ${subjectId},
        weekId: ${weekId},
        semesterId: ${semesterId}`);

      // Convert string parameters to numbers if they exist
      const filters = {
        quizId: quizId ? parseInt(quizId) : undefined,
        lessonId: lessonId ? parseInt(lessonId) : undefined,
        subjectId: subjectId ? parseInt(subjectId) : undefined,
        weekId: weekId ? parseInt(weekId) : undefined,
        semesterId: semesterId ? parseInt(semesterId) : undefined,
      };

      return await this.quizAnswerService.getQuizAnswersSummary(filters);
    } catch (error) {
      this.logger.error(`Error fetching quiz answers summary: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quiz answers summary',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getQuizAnswerById(@Param('id', ParseIntPipe) id: number) {
    try {
      this.logger.debug(`Fetching quiz answer details for ID: ${id}`);
      return await this.quizAnswerService.getQuizAnswerById(id);
    } catch (error) {
      this.logger.error(`Error fetching quiz answer details: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quiz answer details',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('redo')
  async addRedoEntry(@Body() addRedoDto: AddRedoDto) {
    try {
      this.logger.debug(`Adding redo entry for student ${addRedoDto.studentId} and quiz ${addRedoDto.quizId}`);
      return await this.quizAnswerService.addRedoEntry(addRedoDto.studentId, addRedoDto.quizId);
    } catch (error) {
      this.logger.error(`Error adding redo entry: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to add redo entry',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get('quiz/:quizId/summary')
  async getQuizAnswersSummaryByQuizId(@Param('quizId', ParseIntPipe) quizId: number) {
    try {
      this.logger.debug(`Fetching quiz answers summary for quiz ID: ${quizId}`);
      return await this.quizAnswerService.getQuizAnswersSummaryByQuizId(quizId);
    } catch (error) {
      this.logger.error(`Error fetching quiz answers summary: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quiz answers summary',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
