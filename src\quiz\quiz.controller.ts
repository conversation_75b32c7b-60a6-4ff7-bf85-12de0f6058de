import { Body, Controller, Post, Get, Put, Delete, Query, Param, Logger, HttpException, HttpStatus, ParseIntPipe, HttpCode, UseGuards } from '@nestjs/common';
import { QuizService } from './quiz.service';
import { CreateQuizDto } from './dto/create-quiz.dto';
import { UpdateQuizDto } from './dto/update-quiz.dto';
import { JwtGuard } from 'src/auth/guard';
import { GetUser } from 'src/auth/decorator';

@Controller('quiz')
export class QuizController {
  private readonly logger = new Logger(QuizController.name);

  constructor(private readonly quizService: QuizService) {}

  @Post()
  async createQuiz(@Body() createQuizDto: CreateQuizDto) {
    try {
      console.log('Raw request body:', JSON.stringify(createQuizDto, null, 2));
      this.logger.debug(`Creating quiz with data: ${JSON.stringify(createQuizDto)}`);
      return await this.quizService.createQuiz(createQuizDto);
    } catch (error) {
      this.logger.error(`Error creating quiz: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to create quiz',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get()
  async getQuizzes(
    @Query('semesterId') semesterId?: string,
    @Query('subjectId') subjectId?: string,
    @Query('weekId') weekId?: string,
    @Query('lessonId') lessonId?: string,
  ) {
    try {
      const filters = {
        semesterId: semesterId ? parseInt(semesterId, 10) : undefined,
        subjectId: subjectId ? parseInt(subjectId, 10) : undefined,
        weekId: weekId ? parseInt(weekId, 10) : undefined,
        lessonId: lessonId ? parseInt(lessonId, 10) : undefined,
      };

      this.logger.debug(`Fetching quizzes with filters - semesterId: ${filters.semesterId}, subjectId: ${filters.subjectId}, weekId: ${filters.weekId}, lessonId: ${filters.lessonId}`);
      return await this.quizService.getQuizzes(filters);
    } catch (error) {
      this.logger.error(`Error fetching quizzes: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quizzes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Get(':id')
  async getQuizById(@Param('id', ParseIntPipe) id: number) {
    try {
      this.logger.debug(`Fetching quiz details for ID: ${id}`);
      return await this.quizService.getQuizById(id);
    } catch (error) {
      this.logger.error(`Error fetching quiz details: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quiz details',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Put(':id')
  async updateQuiz(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateQuizDto: UpdateQuizDto,
  ) {
    try {
      this.logger.debug(`Updating quiz with ID: ${id}`);
      return await this.quizService.updateQuiz(id, updateQuizDto);
    } catch (error) {
      this.logger.error(`Error updating quiz: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to update quiz',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Delete(':id')
  async deleteQuiz(@Param('id', ParseIntPipe) id: number) {
    try {
      this.logger.debug(`Deleting quiz with ID: ${id}`);
      return await this.quizService.deleteQuiz(id);
    } catch (error) {
      this.logger.error(`Error deleting quiz: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to delete quiz',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  @Post('week/:weekId/quiz')
  async addQuizToWeek(
    @Param('weekId', ParseIntPipe) weekId: number,
    @Body() quizData: { quizId: number }
  ) {
    try {
      return await this.quizService.addQuizToWeek(weekId, quizData);
    } catch (error) {
      this.logger.error(`Error adding quiz to week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to add quiz to week',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Delete('week/:weekId/quiz/:quizId')
  @HttpCode(HttpStatus.OK)
  async removeQuizFromWeek(
    @Param('weekId', ParseIntPipe) weekId: number,
    @Param('quizId', ParseIntPipe) quizId: number
  ) {
    try {
      return await this.quizService.removeQuizFromWeek(weekId, quizId);
    } catch (error) {
      this.logger.error(`Error removing quiz from week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to remove quiz from week',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @UseGuards(JwtGuard)
  @Get('week/:weekId')
  async getQuizzesByWeekId(
    @Param('weekId', ParseIntPipe) weekId: number,
    @GetUser('id') userId: number
  ) {
    try {
      // Get the student ID from the user ID
      const student = await this.quizService['prisma'].student.findUnique({
        where: { userId },
        select: { id: true }
      });

      // Pass the student ID to the service method if the user is a student
      const studentId = student ? student.id : undefined;

      return await this.quizService.getQuizzesByWeekId(weekId, studentId);
    } catch (error) {
      this.logger.error(`Error fetching quizzes for week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quizzes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('week/:weekId/simple')
  async getSimpleQuizzesByWeekId(@Param('weekId', ParseIntPipe) weekId: number) {
    try {
      return await this.quizService.getSimpleQuizzesByWeekId(weekId);
    } catch (error) {
      this.logger.error(`Error fetching simple quizzes for week: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quizzes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get('lesson/:lessonId/simple')
  async getSimpleQuizzesByLessonId(@Param('lessonId', ParseIntPipe) lessonId: number) {
    try {
      this.logger.debug(`Fetching simple quizzes for lesson ID: ${lessonId}`);
      return await this.quizService.getSimpleQuizzesByLessonId(lessonId);
    } catch (error) {
      this.logger.error(`Error fetching simple quizzes for lesson: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quizzes',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  @Get(':id/random')
  async getQuizWithRandomQuestions(@Param('id', ParseIntPipe) id: number) {
    try {
      this.logger.debug(`Fetching quiz with random questions for ID: ${id}`);
      return await this.quizService.getQuizWithRandomQuestions(id);
    } catch (error) {
      this.logger.error(`Error fetching quiz with random questions: ${error.message}`);
      throw new HttpException(
        error.message || 'Failed to fetch quiz',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}








