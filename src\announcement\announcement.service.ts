import { Injectable, NotFoundException, Logger, HttpException, HttpStatus } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateAnnouncementDto } from './dto/create-announcement.dto';
import { UpdateAnnouncementDto } from './dto/update-announcement.dto';

@Injectable()
export class AnnouncementService {
  private readonly logger = new Logger(AnnouncementService.name);

  constructor(private prisma: PrismaService) {}

  async createAnnouncement(createAnnouncementDto: CreateAnnouncementDto) {
    try {
      const { title, description, meetingLink, imageUrl, weekIds } = createAnnouncementDto;

      // Create the announcement
      const announcement = await this.prisma.announcement.create({
        data: {
          title,
          description,
          meetingLink,
          imageUrl,
        },
      });

      // If weekIds are provided, create week announcements
      if (weekIds && weekIds.length > 0) {
        // Verify that all weekIds exist
        const weeks = await this.prisma.week.findMany({
          where: {
            id: {
              in: weekIds,
            },
          },
        });

        if (weeks.length !== weekIds.length) {
          // Some weekIds don't exist
          await this.prisma.announcement.delete({
            where: { id: announcement.id },
          });
          throw new NotFoundException('One or more week IDs do not exist');
        }

        // Create week announcements
        await this.prisma.weekAnnouncement.createMany({
          data: weekIds.map(weekId => ({
            announcementId: announcement.id,
            weekId,
          })),
        });

        // Return the announcement with week information
        const announcementWithWeeks = await this.prisma.announcement.findUnique({
          where: { id: announcement.id },
          include: {
            weeks: {
              include: {
                week: true,
              },
            },
          },
        });

        return {
          message: 'Week announcement created successfully',
          data: announcementWithWeeks,
        };
      }

      return {
        message: 'General announcement created successfully',
        data: announcement,
      };
    } catch (error) {
      this.logger.error(`Failed to create announcement: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        `Failed to create announcement: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllGeneralAnnouncements() {
    try {
      // Get announcements that don't have any week associations
      const announcements = await this.prisma.announcement.findMany({
        where: {
          weeks: {
            none: {},
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'General announcements retrieved successfully',
        data: announcements,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch general announcements: ${error.message}`);
      throw new HttpException(
        `Failed to fetch general announcements: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAllWeekAnnouncements() {
    try {
      // Get announcements that have week associations
      const announcements = await this.prisma.announcement.findMany({
        where: {
          weeks: {
            some: {},
          },
        },
        include: {
          weeks: {
            include: {
              week: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Week announcements retrieved successfully',
        data: announcements,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch week announcements: ${error.message}`);
      throw new HttpException(
        `Failed to fetch week announcements: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAnnouncementsByWeekId(weekId: number) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId },
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Get announcements for the week
      const weekAnnouncements = await this.prisma.weekAnnouncement.findMany({
        where: { weekId },
        include: {
          announcement: true,
        },
      });

      const announcements = weekAnnouncements.map(wa => wa.announcement);

      return {
        message: 'Week announcements retrieved successfully',
        data: announcements,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch announcements for week ${weekId}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch announcements for week: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async getAnnouncementById(id: number) {
    try {
      const announcement = await this.prisma.announcement.findUnique({
        where: { id },
        include: {
          weeks: {
            include: {
              week: true,
            },
          },
        },
      });

      if (!announcement) {
        throw new NotFoundException(`Announcement with ID ${id} not found`);
      }

      return {
        message: 'Announcement retrieved successfully',
        data: announcement,
      };
    } catch (error) {
      this.logger.error(`Failed to fetch announcement with ID ${id}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        `Failed to fetch announcement: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }

  async updateAnnouncement(id: number, updateAnnouncementDto: UpdateAnnouncementDto) {
    try {
      // Check if announcement exists
      const existingAnnouncement = await this.prisma.announcement.findUnique({
        where: { id },
        include: {
          weeks: true,
        },
      });

      if (!existingAnnouncement) {
        throw new NotFoundException(`Announcement with ID ${id} not found`);
      }

      // Start a transaction to ensure all operations succeed or fail together
      return await this.prisma.$transaction(async (prisma) => {
        // Update the announcement basic info
        const { title, description, meetingLink, imageUrl, weekIds } = updateAnnouncementDto;

        const updatedAnnouncement = await prisma.announcement.update({
          where: { id },
          data: {
            ...(title !== undefined && { title }),
            ...(description !== undefined && { description }),
            ...(meetingLink !== undefined && { meetingLink }),
            ...(imageUrl !== undefined && { imageUrl }),
            updatedAt: new Date(),
          },
        });

        // If weekIds are provided, update week associations
        if (weekIds !== undefined) {
          // Verify that all weekIds exist
          if (weekIds.length > 0) {
            const weeks = await prisma.week.findMany({
              where: {
                id: {
                  in: weekIds,
                },
              },
            });

            if (weeks.length !== weekIds.length) {
              throw new NotFoundException('One or more week IDs do not exist');
            }
          }

          // Delete existing week associations
          await prisma.weekAnnouncement.deleteMany({
            where: { announcementId: id },
          });

          // Create new week associations if weekIds is not empty
          if (weekIds.length > 0) {
            await prisma.weekAnnouncement.createMany({
              data: weekIds.map(weekId => ({
                announcementId: id,
                weekId,
              })),
            });
          }
        }

        // Return the updated announcement with week information
        const result = await prisma.announcement.findUnique({
          where: { id },
          include: {
            weeks: {
              include: {
                week: true,
              },
            },
          },
        });

        return {
          message: 'Announcement updated successfully',
          data: result,
        };
      });
    } catch (error) {
      this.logger.error(`Failed to update announcement with ID ${id}: ${error.message}`);
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new HttpException(
        `Failed to update announcement: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR,
      );
    }
  }
}
