import { Controller, Get, UseGuards, Query, Put, Param, Body, ParseIntPipe, HttpException, HttpStatus } from '@nestjs/common';
import { User } from '@prisma/client';
import { GetUser } from 'src/auth/decorator';
import { JwtGuard } from 'src/auth/guard';
import { UserService } from './user.service';
import { UpdateProfilePictureDto } from './dto';


@Controller('user')
export class UserController {
  constructor(private readonly userService: UserService) {}
  @UseGuards(JwtGuard)
  @Get('info')
  async getUserInfo(@GetUser('id') userId: number) {
    return this.userService.getStudentInfo(userId);
  }

  @Get('teachers')
  getAllTeachers() {
    return this.userService.getAllTeachers();
  }
  @Get('students')
  getAllStudents(@Query('isVerified') isVerified?: string) {
    // Convert string query param to boolean if provided
    const isVerifiedBool = isVerified ? isVerified === 'true' : undefined;
    return this.userService.getAllStudents(isVerifiedBool);
  }

  @Get('students/summary')
  getAllStudentsSummary() {
    return this.userService.getAllStudentsSummary();
  }

  @Put('student/:id/verify')
  async verifyStudent(
    @Param('id', ParseIntPipe) studentId: number,
    @Body() body: { action: 'approve' | 'decline' }
  ) {
    if (!['approve', 'decline'].includes(body.action)) {
      throw new HttpException('Action must be either "approve" or "decline"', HttpStatus.BAD_REQUEST);
    }
    return this.userService.verifyStudent(studentId, body.action);
  }

  @UseGuards(JwtGuard)
  @Put('profile-picture')
  async updateProfilePicture(
    @GetUser('id') userId: number,
    @Body() updateProfilePictureDto: UpdateProfilePictureDto
  ) {
    try {
      return await this.userService.updateProfilePicture(userId, updateProfilePictureDto.profilePicture);
    } catch (error) {
      if (error instanceof HttpException) {
        throw error;
      }
      throw new HttpException(
        error.message || 'Failed to update profile picture',
        error.status || HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }
}
