import { Type } from 'class-transformer';
import { <PERSON><PERSON>rray, IsBoolean, IsEnum, IsInt, IsNotEmpty, IsOptional, ValidateNested } from 'class-validator';

// Define answer types enum
enum AnswerType {
  MCQ = 'mcq',
  TEXT = 'text',
  RECORD = 'record'
}

// Base class for different types of answers
class BaseAnswer {
  @IsEnum(AnswerType)
  @IsNotEmpty()
  type: AnswerType;

  @IsInt()
  @IsNotEmpty()
  questionId: number;

  // Question text provided by the user
  @IsNotEmpty()
  question: string;

  // Question grade provided by the user
  @IsInt()
  @IsNotEmpty()
  questionGrade: number;
}

// MCQ answer
class MCQAnswer extends BaseAnswer {
  @IsInt()
  @IsNotEmpty()
  selectedAnswerId: number;

  // Selected answer text provided by the user
  @IsNotEmpty()
  userAnswer: string;

  // Whether the answer is correct (will be filled by service)
  @IsOptional()
  isCorrect?: boolean;
}

// Text answer
class TextAnswer extends BaseAnswer {
  // User's text answer
  @IsNotEmpty()
  userAnswer: string;
}

// Record answer (for audio recordings)
class RecordAnswer extends BaseAnswer {
  // Recording URL as the user's answer
  @IsNotEmpty()
  userAnswer: string;
}

export class CreateQuizAnswerDto {
  @IsInt()
  @IsNotEmpty()
  quizId: number;

  @IsInt()
  @IsNotEmpty()
  timeTaken: number; // Time taken in seconds

  @IsInt()
  @IsOptional()
  semesterId?: number; // Will be fetched from the quiz if not provided

  @IsInt()
  @IsOptional()
  lessonId?: number; // Will be fetched from the quiz if not provided

  @IsInt()
  @IsOptional()
  weekId?: number; // Will be fetched based on the current week

  @IsInt()
  @IsOptional()
  subjectId?: number; // Will be fetched from the quiz if not provided

  @IsInt()
  @IsOptional()
  finalGrade?: number; // Will be calculated based on auto-graded MCQ questions

  @IsBoolean()
  @IsOptional()
  redo?: boolean; // Optional flag to indicate this is a redo submission

  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => Object, {
    keepDiscriminatorProperty: true,
    discriminator: {
      property: 'type',
      subTypes: [
        { value: MCQAnswer, name: 'mcq' },
        { value: TextAnswer, name: 'text' },
        { value: RecordAnswer, name: 'record' },
      ],
    },
  })
  answers: (MCQAnswer | TextAnswer | RecordAnswer)[];

  // This field is not part of the request body, but will be set in the controller
  studentId?: number;
}
