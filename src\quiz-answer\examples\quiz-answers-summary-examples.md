# Quiz Answers Summary API Examples

## Get Quiz Answers Summary

This endpoint returns a list of quiz answers with basic information that can be used to display a summary view. The results can be filtered by various criteria.

### Endpoint

```
GET /quiz-answers/summary
```

### Query Parameters

All parameters are optional:

- `quizId`: Filter by quiz ID
- `lessonId`: Filter by lesson ID
- `subjectId`: Filter by subject ID
- `weekId`: Filter by week ID
- `semesterId`: Filter by semester ID

### Example Requests

#### Get All Quiz Answers

```
GET /quiz-answers/summary
```

#### Filter by Quiz ID

```
GET /quiz-answers/summary?quizId=3
```

#### Filter by Subject and Lesson

```
GET /quiz-answers/summary?subjectId=1&lessonId=1
```

#### Filter by Week and Semester

```
GET /quiz-answers/summary?weekId=2&semesterId=1
```

### Example Response

```json
{
  "message": "Quiz answers summary retrieved successfully",
  "data": [
    {
      "id": 5,
      "studentName": "<PERSON>",
      "studentCode": "ST12345",
      "quizName": "test2",
      "finalGrade": 9,
      "grade": 9,
      "timeTaken": 800,
      "autoGraded": true,
      "createdAt": "2025-05-22T19:15:38.476Z"
    },
    {
      "id": 4,
      "studentName": "Jane Smith",
      "studentCode": "ST67890",
      "quizName": "test2",
      "finalGrade": 9,
      "grade": null,
      "timeTaken": 1200,
      "autoGraded": false,
      "createdAt": "2025-05-22T18:45:12.123Z"
    },
    {
      "id": 3,
      "studentName": "Ahmed Hassan",
      "studentCode": "ST54321",
      "quizName": "test2",
      "finalGrade": 9,
      "grade": 6,
      "timeTaken": 950,
      "autoGraded": true,
      "createdAt": "2025-05-22T18:30:45.789Z"
    }
  ]
}
```

## Get Quiz Answers by Quiz ID

This endpoint returns all quiz answers for a specific quiz.

### Endpoint

```
GET /quiz-answers/quiz/:quizId
```

### Example Request

```
GET /quiz-answers/quiz/3
```

### Example Response

```json
{
  "message": "Quiz answers retrieved successfully",
  "data": [
    {
      "id": 5,
      "studentName": "John Doe",
      "studentCode": "ST12345",
      "quizName": "test2",
      "finalGrade": 9,
      "grade": 9,
      "timeTaken": 800,
      "autoGraded": true,
      "createdAt": "2025-05-22T19:15:38.476Z"
    },
    {
      "id": 4,
      "studentName": "Jane Smith",
      "studentCode": "ST67890",
      "quizName": "test2",
      "finalGrade": 9,
      "grade": null,
      "timeTaken": 1200,
      "autoGraded": false,
      "createdAt": "2025-05-22T18:45:12.123Z"
    },
    {
      "id": 3,
      "studentName": "Ahmed Hassan",
      "studentCode": "ST54321",
      "quizName": "test2",
      "finalGrade": 9,
      "grade": 6,
      "timeTaken": 950,
      "autoGraded": true,
      "createdAt": "2025-05-22T18:30:45.789Z"
    }
  ]
}
```

## Notes

1. The summary endpoints return only essential information about each quiz answer:
   - Student name and code
   - Quiz name
   - Final grade (the maximum possible grade)
   - Grade (the actual points earned, null if not auto-graded)
   - Time taken (in seconds)
   - Whether the quiz was auto-graded
   - Creation date

2. These endpoints are designed for displaying a list of quiz answers that can be clicked to view more details.

3. The detailed view of a specific quiz answer with questions and answers can be accessed using the existing endpoint:
   ```
   GET /quiz-answers/:answerId/quiz/:quizId
   ```
