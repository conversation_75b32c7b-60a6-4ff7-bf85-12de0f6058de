import { Modu<PERSON> } from '@nestjs/common';
import { ConfigModule } from '@nestjs/config';

import { AuthModule } from './auth/auth.module';
import { UserModule } from './user/user.module';
import { PrismaModule } from './prisma/prisma.module';
import { SemesterModule } from './semester/semester.module';
import { SubjectModule } from './subject/subject.module';
import { QuizModule } from './quiz/quiz.module';
import { AnnouncementModule } from './announcement/announcement.module';
import { QuizAnswerModule } from './quiz-answer/quiz-answer.module';
import { GradesModule } from './grades/grades.module';

@Module({
  imports: [
    AuthModule,
    UserModule,
    PrismaModule,
    ConfigModule.forRoot({
      isGlobal: true,
    }),
    SemesterModule,
    SubjectModule,
    QuizModule,
    AnnouncementModule,
    QuizAnswerModule,
    GradesModule
  ],
})
export class AppModule { }
