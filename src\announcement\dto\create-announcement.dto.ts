import { IsNotEmpty, Is<PERSON>ptional, IsString, <PERSON><PERSON>nt, <PERSON>Array } from 'class-validator';

export class CreateAnnouncementDto {
  @IsNotEmpty({ message: 'Title is required' })
  @IsString({ message: 'Title must be a string' })
  title: string;

  @IsNotEmpty({ message: 'Description is required' })
  @IsString({ message: 'Description must be a string' })
  description: string;

  @IsOptional()
  @IsString({ message: 'Meeting link must be a string' })
  meetingLink?: string;

  @IsOptional()
  @IsString({ message: 'Image URL must be a string' })
  imageUrl?: string;

  @IsOptional()
  @IsArray({ message: 'Week IDs must be an array' })
  @IsInt({ each: true, message: 'Each week ID must be an integer' })
  weekIds?: number[];
}
