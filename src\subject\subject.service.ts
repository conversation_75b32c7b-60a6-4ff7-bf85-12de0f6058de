import { Injectable, NotFoundException } from '@nestjs/common';
import { PrismaService } from 'src/prisma/prisma.service';
import { CreateSubjectDto } from './dto/subject.dto';
import { CreateLessonDto } from './dto/create-lesson.dto';
import { CreateItemDto } from './dto/create-item.dto';
import { AddSubCategoryDto } from './dto/subcategory.dto';

@Injectable()
export class SubjectService {
  constructor(private prisma: PrismaService) {}

  async createSubject(createSubjectDto: CreateSubjectDto) {
    try {
      // Check if semester template exists
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id: createSubjectDto.semesterTemplateId },
      });

      if (!template) {
        throw new NotFoundException(
          `Semester template with ID ${createSubjectDto.semesterTemplateId} not found`,
        );
      }

      // Check if teacher exists if teacherId is provided
      if (createSubjectDto.teacherId) {
        const teacher = await this.prisma.teacher.findUnique({
          where: { id: createSubjectDto.teacherId },
        });

        if (!teacher) {
          throw new NotFoundException(
            `Teacher with ID ${createSubjectDto.teacherId} not found`,
          );
        }
      }

      const subject = await this.prisma.subject.create({
        data: {
          name: createSubjectDto.name,
          code: createSubjectDto.code,
          teacherId: createSubjectDto.teacherId || null,  // Allow null
          semesterTemplateId: createSubjectDto.semesterTemplateId,
          subCategories: createSubjectDto.subCategories ? JSON.stringify(createSubjectDto.subCategories) : null,
        },
        include: {
          semesterTemplate: true,
          teacher: true,
        },
      });

      return {
        message: 'Subject created successfully',
        data: subject,
      };
    } catch (error) {
      throw new Error(`Failed to create subject: ${error.message}`);
    }
  }

  async getSubjectsByTemplateId(semesterTemplateId: number) {
    try {
      // First check if the semester template exists
      const template = await this.prisma.semesterTemplate.findUnique({
        where: { id: semesterTemplateId },
      });

      if (!template) {
        throw new NotFoundException(
          `Semester template with ID ${semesterTemplateId} not found`,
        );
      }

      // Fetch all subjects for this template
      const subjects = await this.prisma.subject.findMany({
        where: {
          semesterTemplateId,
        },
        include: {
          teacher: true,
          semesterTemplate: true,
        },
      });

      // Parse subcategories for each subject
      const subjectsWithParsedSubCategories = subjects.map(subject => ({
        ...subject,
        subCategories: subject.subCategories
          ? JSON.parse(subject.subCategories as string)
          : [],
      }));

      return {
        message: 'Subjects retrieved successfully',
        data: subjectsWithParsedSubCategories,
      };
    } catch (error) {
      throw new Error(`Failed to fetch subjects: ${error.message}`);
    }
  }

  async createLesson(createLessonDto: CreateLessonDto) {
    try {
      // Check if subject exists
      const subject = await this.prisma.subject.findUnique({
        where: { id: createLessonDto.subjectId },
      });

      if (!subject) {
        throw new NotFoundException(
          `Subject with ID ${createLessonDto.subjectId} not found`,
        );
      }

      const lesson = await this.prisma.lesson.create({
        data: {
          name: createLessonDto.name,
          subjectId: createLessonDto.subjectId,
        },
        include: {
          subject: true,
          Items: true,
        },
      });

      return {
        message: 'Lesson created successfully',
        data: lesson,
      };
    } catch (error) {
      throw new Error(`Failed to create lesson: ${error.message}`);
    }
  }

  async createItem(createItemDto: CreateItemDto) {
    try {
      // Check if lesson exists
      const lesson = await this.prisma.lesson.findUnique({
        where: { id: createItemDto.lessonId },
      });

      if (!lesson) {
        throw new NotFoundException(
          `Lesson with ID ${createItemDto.lessonId} not found`,
        );
      }

      const item = await this.prisma.item.create({
        data: {
          LessonId: createItemDto.lessonId,
          title: createItemDto.title,
          itemType: createItemDto.itemType,
          itemContent: createItemDto.itemContent,
        },
        include: {
          lesson: true,
        },
      });

      return {
        message: 'Item created successfully',
        data: item,
      };
    } catch (error) {
      throw new Error(`Failed to create item: ${error.message}`);
    }
  }

  async getLessonsBySubjectId(subjectId: number) {
    try {
      // Check if subject exists
      const subject = await this.prisma.subject.findUnique({
        where: { id: subjectId },
      });

      if (!subject) {
        throw new NotFoundException(`Subject with ID ${subjectId} not found`);
      }

      // Fetch all lessons for this subject
      const lessons = await this.prisma.lesson.findMany({
        where: {
          subjectId,
        },
        include: {
          Items: true,
          subject: true,
        },
      });

      // Parse subcategories for the subject in each lesson
      const lessonsWithParsedSubCategories = lessons.map(lesson => ({
        ...lesson,
        subject: {
          ...lesson.subject,
          subCategories: lesson.subject.subCategories
            ? JSON.parse(lesson.subject.subCategories as string)
            : []
        }
      }));

      return {
        message: 'Lessons retrieved successfully',
        data: lessonsWithParsedSubCategories,
      };
    } catch (error) {
      throw new Error(`Failed to fetch lessons: ${error.message}`);
    }
  }

  async getItemsByLessonId(lessonId: number) {
    try {
      // Check if lesson exists
      const lesson = await this.prisma.lesson.findUnique({
        where: { id: lessonId },
      });

      if (!lesson) {
        throw new NotFoundException(`Lesson with ID ${lessonId} not found`);
      }

      // Fetch all items for this lesson
      const items = await this.prisma.item.findMany({
        where: {
          LessonId: lessonId,
        },
        include: {
          lesson: {
            include: {
              subject: true,
            }
          },
        },
      });

      // Parse subcategories for the subject in lesson
      const itemsWithParsedSubCategories = items.map(item => ({
        ...item,
        lesson: {
          ...item.lesson,
          subject: item.lesson.subject ? {
            ...item.lesson.subject,
            subCategories: item.lesson.subject.subCategories
              ? JSON.parse(item.lesson.subject.subCategories as string)
              : []
          } : item.lesson.subject
        }
      }));

      return {
        message: 'Items retrieved successfully',
        data: itemsWithParsedSubCategories,
      };
    } catch (error) {
      throw new Error(`Failed to fetch items: ${error.message}`);
    }
  }

  async deleteItem(itemId: number) {
    try {
      // Check if item exists
      const item = await this.prisma.item.findUnique({
        where: { id: itemId },
      });

      if (!item) {
        throw new NotFoundException(`Item with ID ${itemId} not found`);
      }

      // Delete the item
      await this.prisma.item.delete({
        where: { id: itemId },
      });

      return {
        message: 'Item deleted successfully',
        data: null
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to delete item: ${error.message}`);
    }
  }

  async addSubCategoryToSubject(subjectId: number, addSubCategoryDto: AddSubCategoryDto) {
    try {
      // Check if subject exists
      const subject = await this.prisma.subject.findUnique({
        where: { id: subjectId },
      });

      if (!subject) {
        throw new NotFoundException(`Subject with ID ${subjectId} not found`);
      }

      // Parse existing subcategories or initialize empty array
      const existingSubCategories = subject.subCategories
        ? JSON.parse(subject.subCategories as string)
        : [];

      // Generate new ID (find max ID and add 1)
      const maxId = existingSubCategories.length > 0
        ? Math.max(...existingSubCategories.map((cat: any) => cat.id))
        : 0;

      const newSubCategory = {
        id: maxId + 1,
        name: addSubCategoryDto.name,
      };

      // Add new subcategory
      const updatedSubCategories = [...existingSubCategories, newSubCategory];

      // Update subject with new subcategories
      const updatedSubject = await this.prisma.subject.update({
        where: { id: subjectId },
        data: {
          subCategories: JSON.stringify(updatedSubCategories),
        },
        include: {
          semesterTemplate: true,
          teacher: true,
        },
      });

      return {
        message: 'Subcategory added successfully',
        data: {
          ...updatedSubject,
          subCategories: updatedSubCategories,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to add subcategory: ${error.message}`);
    }
  }

  async deleteSubCategoryFromSubject(subjectId: number, subCategoryId: number) {
    try {
      // Check if subject exists
      const subject = await this.prisma.subject.findUnique({
        where: { id: subjectId },
      });

      if (!subject) {
        throw new NotFoundException(`Subject with ID ${subjectId} not found`);
      }

      // Parse existing subcategories
      const existingSubCategories = subject.subCategories
        ? JSON.parse(subject.subCategories as string)
        : [];

      // Check if subcategory exists
      const subCategoryExists = existingSubCategories.find((cat: any) => cat.id === subCategoryId);
      if (!subCategoryExists) {
        throw new NotFoundException(`Subcategory with ID ${subCategoryId} not found`);
      }

      // Remove subcategory
      const updatedSubCategories = existingSubCategories.filter((cat: any) => cat.id !== subCategoryId);

      // Update subject
      const updatedSubject = await this.prisma.subject.update({
        where: { id: subjectId },
        data: {
          subCategories: updatedSubCategories.length > 0 ? JSON.stringify(updatedSubCategories) : null,
        },
        include: {
          semesterTemplate: true,
          teacher: true,
        },
      });

      return {
        message: 'Subcategory deleted successfully',
        data: {
          ...updatedSubject,
          subCategories: updatedSubCategories,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to delete subcategory: ${error.message}`);
    }
  }

  async getSubCategoriesBySubjectId(subjectId: number) {
    try {
      // Check if subject exists
      const subject = await this.prisma.subject.findUnique({
        where: { id: subjectId },
        select: {
          id: true,
          name: true,
          code: true,
          subCategories: true,
        },
      });

      if (!subject) {
        throw new NotFoundException(`Subject with ID ${subjectId} not found`);
      }

      // Parse subcategories
      const subCategories = subject.subCategories
        ? JSON.parse(subject.subCategories as string)
        : [];

      return {
        message: 'Subcategories retrieved successfully',
        data: {
          subject: {
            id: subject.id,
            name: subject.name,
            code: subject.code,
          },
          subCategories,
        },
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch subcategories: ${error.message}`);
    }
  }
}



