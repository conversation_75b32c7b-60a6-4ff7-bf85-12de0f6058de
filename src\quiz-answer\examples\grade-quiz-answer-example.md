# Grade Quiz Answer API Example

## Grade a Quiz Answer

This endpoint allows you to manually grade a quiz answer by providing a grade value.

### Endpoint

```
PUT /quiz-answers/:id/grade
```

### Request Body

```json
{
  "grade": 7
}
```

### Example Request

```
PUT /quiz-answers/4/grade
Content-Type: application/json

{
  "grade": 7
}
```

### Example Response

```json
{
  "message": "Quiz answer graded successfully",
  "data": {
    "id": 4,
    "quizId": 3,
    "quizName": "test2",
    "studentName": "<PERSON>",
    "studentCode": "ST67890",
    "grade": 7,
    "finalGrade": 9,
    "autoGraded": false,
    "answers": [
      {
        "type": "mcq",
        "question": "fd",
        "questionGrade": 1,
        "userAnswer": "ds",
        "isCorrect": true
      },
      {
        "type": "text",
        "question": "gfds",
        "questionGrade": 3,
        "userAnswer": "This is my answer to the text question",
        "correctAnswer": "fd"
      },
      {
        "type": "mcq",
        "question": "hgfdsa",
        "questionGrade": 5,
        "userAnswer": "gh",
        "isCorrect": true
      }
    ],
    "updatedAt": "2025-05-22T20:15:38.476Z"
  }
}
```

## Notes

1. **Request Parameters**:
   - `id`: The ID of the quiz answer to grade (in the URL)
   - `grade`: The grade to assign to the quiz answer (in the request body)

2. **Validation**:
   - The grade must be a non-negative integer
   - The grade cannot exceed the maximum grade for the quiz
   - If the grade is higher than the maximum grade, the request will fail with a 400 Bad Request error

3. **Response**:
   - The response includes the updated quiz answer with the new grade
   - The `autoGraded` field is set to `false` to indicate that the quiz was manually graded
   - The `answers` array contains all the answers submitted by the student

4. **Use Cases**:
   - Manually grade quizzes that contain text or recording questions
   - Override the auto-graded score for MCQ-only quizzes if needed

5. **Error Handling**:
   - If the quiz answer is not found, a 404 Not Found error is returned
   - If the grade is higher than the maximum grade, a 400 Bad Request error is returned
   - If any other error occurs, a 500 Internal Server Error is returned
