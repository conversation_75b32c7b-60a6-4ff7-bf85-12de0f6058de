# Semester API Documentation

## Get Subjects and Lessons by Week ID

```
GET /semester/week/:weekId/subjects
```

Retrieves all subjects and lessons for a specific week, including quizzes with the number of attempts made by the authenticated user.

### Authentication

This endpoint requires JWT authentication. The user's ID is extracted from the JWT token to determine the student ID.

### Parameters

- `weekId` (path parameter): The ID of the week to retrieve subjects and lessons for.

### Response

```json
{
  "message": "Subjects, lessons and quizzes retrieved successfully",
  "data": {
    "subjects": [
      {
        "id": 1,
        "name": "Biblical Studies",
        "code": "BIB101",
        "lessons": [
          {
            "id": 1,
            "name": "Introduction to the Old Testament",
            "items": [
              {
                "id": 1,
                "LessonId": 1,
                "title": "The Pentateuch",
                "itemType": "pdf",
                "itemContent": "https://example.com/pentateuch.pdf"
              }
            ],
            "quizzes": [
              {
                "id": 5,
                "name": "Old Testament Quiz",
                "timeLimit": 20,
                "grade": 50,
                "numberOfAttempts": 2,
                "type": "lesson",
                "lessonId": 1,
                "userAttempts": 1
              },
              {
                "id": 6,
                "name": "Week 1 Quiz",
                "timeLimit": 30,
                "grade": 100,
                "numberOfAttempts": 3,
                "type": "weekly",
                "lessonId": 1,
                "userAttempts": 2
              }
            ],
            "order": 1
          }
        ]
      }
    ]
  }
}
```

### Notes

- All quizzes are now organized within their respective lessons in the `quizzes` array.
- Quizzes can have different types (e.g., "lesson", "weekly", "final") but they are all associated with a specific lesson.
- The `userAttempts` field in each quiz shows how many times the authenticated user has attempted the quiz.
- This count is compared against the `numberOfAttempts` field to determine if the user can still attempt the quiz.
- If the user is not a student or not authenticated, the `userAttempts` will be 0.

## Implementation Details

The endpoint works as follows:

1. The user's ID is extracted from the JWT token.
2. The system looks up the student record associated with this user ID.
3. If a student record is found, the student ID is passed to the service method.
4. The service retrieves all subjects, lessons, and quizzes for the specified week.
5. The service organizes quizzes within their respective lessons:
   - Each lesson contains its own quizzes in the `quizzes` array
   - Quizzes of all types (lesson, weekly, final) are included with their lessons
6. For each quiz, the service counts how many times the student has attempted it.
7. This count is included in the response as `userAttempts` for each quiz.

This organization provides several benefits:
- Better representation of the relationship between lessons and quizzes
- Easier for the frontend to display quizzes in the context of their lessons
- Allows the frontend to show the user how many attempts they have made and how many they have remaining for each quiz
- Simplifies the data structure by having all quizzes within their lessons
