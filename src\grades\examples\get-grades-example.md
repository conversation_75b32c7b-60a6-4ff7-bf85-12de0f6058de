# Get Grades by <PERSON><PERSON><PERSON> and Student API Example

## Get Student Grades for a Semester

This endpoint retrieves all grades for a specific student in a specific semester. It aggregates quiz grades by subject and separates final quizzes from week quizzes.

### Endpoint

```
GET /grades/semester/:semesterId/student/:studentId
```

### Path Parameters

- `semesterId` (number): The ID of the semester
- `studentId` (number): The ID of the student

### Example Request

```
GET /grades/semester/1/student/5
```

### Example Response (Success)

```json
{
  "message": "Grades retrieved successfully",
  "data": {
    "semester": {
      "id": 1,
      "name": "Spring 2025"
    },
    "student": {
      "id": 5,
      "name": "<PERSON>",
      "code": "STU5"
    },
    "subjects": [
      {
        "id": 1,
        "name": "الحان",
        "code": "MUS101",
        "quizzes": [
          {
            "name": "Final Exam - Music Theory",
            "userGrade": 85,
            "finalGrade": 100
          },
          {
            "name": "Final Exam - Practical",
            "userGrade": null,
            "finalGrade": 100
          },
          {
            "name": "Week Quizzes",
            "userGrade": 45,
            "finalGrade": 50
          }
        ]
      },
      {
        "id": 2,
        "name": "اللاهوت",
        "code": "THE101",
        "quizzes": [
          {
            "name": "Final Exam - Theology",
            "userGrade": 92,
            "finalGrade": 100
          },
          {
            "name": "Week Quizzes",
            "userGrade": 15,
            "finalGrade": 30
          }
        ]
      },
      {
        "id": 3,
        "name": "التاريخ",
        "code": "HIS101",
        "quizzes": [
          {
            "name": "Final Exam - Church History",
            "userGrade": 78,
            "finalGrade": 100
          }
        ]
      }
    ]
  }
}
```

### Example Response (Student Not Found)

```json
{
  "message": "Student with ID 999 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

### Example Response (Semester Not Found)

```json
{
  "message": "Semester with ID 999 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

## Response Structure

### Success Response

- **message**: Success message
- **data**: Object containing:
  - **semester**: Object with semester information
    - **id**: Semester ID
    - **name**: Semester name
  - **student**: Object with student information
    - **id**: Student ID
    - **name**: Student's full name
    - **code**: Student code
  - **subjects**: Array of subject objects, each containing:
    - **id**: Subject ID
    - **name**: Subject name
    - **code**: Subject code
    - **quizzes**: Array of quiz grade objects, each containing:
      - **name**: Quiz name (or "Week Quizzes" for aggregated week quizzes)
      - **userGrade**: Student's grade (null if not attempted or not graded)
      - **finalGrade**: Maximum possible grade

## Grade Aggregation Logic

### Final Quizzes
- Each final quiz (type: "Final") is displayed individually
- Shows the student's grade from their latest submission
- If no submission exists or grade is null, userGrade is null

### Week Quizzes
- All week quizzes (type: "Week") for a subject are aggregated into a single entry
- **Name**: Always "Week Quizzes"
- **finalGrade**: Sum of all week quiz maximum grades
- **userGrade**: Sum of all week quiz grades the student achieved
  - Null grades are replaced with 0 in the calculation
  - Only shows null if ALL week quizzes have no attempts or are all pending grading
  - Partial completion shows the sum of completed quizzes (with 0 for incomplete ones)

## Week Quiz Aggregation Examples

### Scenario 1: All Week Quizzes Completed
- Week Quiz 1: 8/10 points
- Week Quiz 2: 7/10 points
- Week Quiz 3: 5/10 points
- **Result**: userGrade = 20, finalGrade = 30

### Scenario 2: Partial Completion (Some Null Grades)
- Week Quiz 1: 8/10 points
- Week Quiz 2: null (not attempted)
- Week Quiz 3: 7/10 points
- **Result**: userGrade = 15 (8 + 0 + 7), finalGrade = 30

### Scenario 3: All Week Quizzes Null
- Week Quiz 1: null (not attempted)
- Week Quiz 2: null (pending grading)
- Week Quiz 3: null (not attempted)
- **Result**: userGrade = null, finalGrade = 30

### Scenario 4: Mixed with Pending Grades
- Week Quiz 1: 8/10 points
- Week Quiz 2: null (pending manual grading)
- Week Quiz 3: 0/10 points (failed)
- **Result**: userGrade = 8 (8 + 0 + 0), finalGrade = 30

## Use Cases

1. **Student Dashboard**: Display student's current grades across all subjects
2. **Academic Progress Tracking**: Monitor student performance throughout the semester
3. **Grade Reports**: Generate comprehensive grade reports for students
4. **Parent/Guardian Access**: Allow parents to view their child's academic progress
5. **Administrative Review**: Enable administrators to review student performance

## Notes

- Only the latest submission for each quiz is considered for grading
- Week quizzes are automatically aggregated to provide a cleaner view
- Null grades indicate either no submission or pending manual grading
- The endpoint validates both semester and student existence before processing
- Grades are organized by subject for better readability
