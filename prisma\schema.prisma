generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

model User {
  id            Int       @id @default(autoincrement())
  email         String    @unique
  password      String
  createdAt     DateTime  @default(now())
  updatedAt     DateTime  @updatedAt
  hashedRt      String?
  name          String?
  birthday      DateTime?
  nationality   String?
  Address       String?
  gender        String?
  isConfirmed   Boolean   @default(false)
  phone         String
  profilePicture String?
  student       Student?
  teacher       Teacher?
}

model Student {
  id                 Int                  @id @default(autoincrement())
  userId             Int                  @unique
  studentCode        String
  city               String
  church             String
  AbEle3traf         String
  deaconLevel        String
  churchService      String
  qualifications     String
  personalIDFront    String
  personalIDBack     String
  isVerified         Boolean              @default(false)
  Tazkia             String
  CompletedSemesters CompletedSemesters[]
  user               User                 @relation(fields: [userId], references: [id])
  semester           Semester[]           @relation("SemesterToStudent")
  quizAnswers        QuizAnswer[]
  redos              Redo[]

  @@index([userId])
  @@map("students")
}

model Teacher {
  id              Int        @id @default(autoincrement())
  userId          Int        @unique
  teacherCode     String
  qualifications  String
  personalIDFront String
  personalIDBack  String
  isVerified      Boolean    @default(false)
  Tazkia          String
  semesterId      Int?
  Subjects        Subject[]
  user            User       @relation(fields: [userId], references: [id])
  semester        Semester[] @relation("SemesterToTeacher")

  @@map("teachers")
}

model Semester {
  id                 Int                  @id @default(autoincrement())
  semesterTemplateId Int
  year               Int
  name               String?
  startDate          DateTime
  endDate            DateTime?
  isCurrent          Boolean              @default(false)
  createdAt          DateTime             @default(now())
  updatedAt          DateTime             @updatedAt
  CompletedSemesters CompletedSemesters[]
  Weeks              Week[]
  semesterTemplate   SemesterTemplate     @relation(fields: [semesterTemplateId], references: [id])
  students           Student[]            @relation("SemesterToStudent")
  Teachers           Teacher[]            @relation("SemesterToTeacher")
  quizAnswers        QuizAnswer[]

  @@map("semesters")
}

model SemesterTemplate {
  id         Int        @id @default(autoincrement())
  semesterNo Int
  name       String?
  createdAt  DateTime   @default(now())
  updatedAt  DateTime   @updatedAt
  Subjects   Subject[]
  Semesters  Semester[]
}

model CompletedSemesters {
  id         Int      @id @default(autoincrement())
  userId     Int
  semesterId Int
  finalGrade Int
  createdAt  DateTime @default(now())
  updatedAt  DateTime @updatedAt
  semester   Semester @relation(fields: [semesterId], references: [id])
  student    Student  @relation(fields: [userId], references: [id])

  @@index([userId])
  @@index([semesterId])
}

model Subject {
  id                 Int              @id @default(autoincrement())
  name               String
  code               String
  semesterTemplateId Int
  teacherId          Int?
  subCategories      Json?            // Array of {id: number, name: string}
  createdAt          DateTime         @default(now())
  updatedAt          DateTime         @updatedAt
  Lessons            Lesson[]
  semesterTemplate   SemesterTemplate @relation(fields: [semesterTemplateId], references: [id])
  teacher            Teacher?         @relation(fields: [teacherId], references: [id])
  quiz               quiz[]
  quizAnswers        QuizAnswer[]
}

model Week {
  id            Int                @id @default(autoincrement())
  weekNo        Int
  semesterId    Int
  startDate     DateTime
  endDate       DateTime
  createdAt     DateTime           @default(now())
  semester      Semester           @relation(fields: [semesterId], references: [id])
  announcements WeekAnnouncement[]
  lessons       WeekLesson[]
  quizAnswers   QuizAnswer[]
}

model WeekLesson {
  id        Int      @id @default(autoincrement())
  weekId    Int
  lessonId  Int
  order     Int
  createdAt DateTime @default(now())
  lesson    Lesson   @relation(fields: [lessonId], references: [id])
  week      Week     @relation(fields: [weekId], references: [id])

  @@unique([weekId, lessonId])
  @@index([weekId])
  @@index([lessonId])
}

model Lesson {
  id          Int          @id @default(autoincrement())
  name        String
  subjectId   Int
  createdAt   DateTime     @default(now())
  Items       Item[]
  subject     Subject      @relation(fields: [subjectId], references: [id])
  weeks       WeekLesson[]
  quizzes     quiz[]
  quizAnswers QuizAnswer[]
}

model Item {
  id          Int      @id @default(autoincrement())
  LessonId    Int
  title       String
  itemType    String
  itemContent String
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  lesson      Lesson   @relation(fields: [LessonId], references: [id])
}

model quiz {
  id               Int          @id @default(autoincrement())
  name             String
  subjectId        Int
  grade            Int
  createdAt        DateTime     @default(now())
  updatedAt        DateTime     @updatedAt
  type             String
  numberOfAttempts Int
  timeLimit        Int?
  content          Json
  lessonId         Int?
  isRecord         Boolean?     @default(false)
  subCategory      Json?        // Single {id: number, name: string}
  lesson           Lesson?      @relation(fields: [lessonId], references: [id])
  subject          Subject      @relation(fields: [subjectId], references: [id])
  answers          QuizAnswer[]
  redos            Redo[]
}

model Announcement {
  id          Int                @id @default(autoincrement())
  title       String
  description String
  meetingLink String?
  imageUrl    String?
  createdAt   DateTime           @default(now())
  updatedAt   DateTime           @updatedAt
  weeks       WeekAnnouncement[]
}

model WeekAnnouncement {
  id             Int          @id @default(autoincrement())
  announcementId Int
  weekId         Int
  createdAt      DateTime     @default(now())
  updatedAt      DateTime     @updatedAt
  announcement   Announcement @relation(fields: [announcementId], references: [id])
  week           Week         @relation(fields: [weekId], references: [id])

  @@index([announcementId])
  @@index([weekId])
}

model QuizAnswer {
  id          Int      @id @default(autoincrement())
  studentId   Int
  quizId      Int
  semesterId  Int
  lessonId    Int
  weekId      Int
  subjectId   Int
  answers     Json
  finalGrade  Int
  grade       Int?
  timeTaken   Int?     // Time taken in seconds
  autoGraded  Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  student     Student  @relation(fields: [studentId], references: [id])
  quiz        quiz     @relation(fields: [quizId], references: [id])
  semester    Semester @relation(fields: [semesterId], references: [id])
  lesson      Lesson   @relation(fields: [lessonId], references: [id])
  week        Week     @relation(fields: [weekId], references: [id])
  subject     Subject  @relation(fields: [subjectId], references: [id])

  @@index([studentId])
  @@index([quizId])
  @@index([semesterId])
  @@index([lessonId])
  @@index([weekId])
  @@index([subjectId])
  @@map("quizAnswer")
}

model Redo {
  id        Int      @id @default(autoincrement())
  studentId Int
  quizId    Int
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  student   Student @relation(fields: [studentId], references: [id])
  quiz      quiz    @relation(fields: [quizId], references: [id])

  @@index([studentId])
  @@index([quizId])
  @@map("redos")
}


