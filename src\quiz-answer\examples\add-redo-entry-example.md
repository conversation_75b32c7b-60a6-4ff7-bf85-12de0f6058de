# Add Redo Entry API Example

## Add Redo Entry

This endpoint allows administrators or teachers to add a redo entry for a specific student and quiz combination. This grants the student permission to retake a quiz even if they have exceeded the maximum number of attempts or missed the deadline.

### Endpoint

```
POST /quiz-answers/redo
```

### Request Body

```json
{
  "studentId": 5,
  "quizId": 3
}
```

### Example Request

```
POST /quiz-answers/redo
Content-Type: application/json

{
  "studentId": 5,
  "quizId": 3
}
```

### Example Response (Success)

```json
{
  "message": "Redo entry added successfully",
  "data": {
    "id": 1,
    "studentId": 5,
    "studentName": "<PERSON> Doe",
    "studentCode": "ST12345",
    "quizId": 3,
    "quizName": "test2",
    "createdAt": "2025-05-22T21:15:38.476Z"
  }
}
```

### Example Response (Error - Already Exists)

```json
{
  "message": "Redo entry already exists for student 5 and quiz 3",
  "error": "Bad Request",
  "statusCode": 400
}
```

### Example Response (Error - Student Not Found)

```json
{
  "message": "Student with ID 5 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

### Example Response (Error - Quiz Not Found)

```json
{
  "message": "Quiz with ID 3 not found",
  "error": "Not Found",
  "statusCode": 404
}
```

## Validation Rules

1. **Student ID**: Must be a valid integer and the student must exist in the database
2. **Quiz ID**: Must be a valid integer and the quiz must exist in the database
3. **Uniqueness**: The combination of studentId and quizId must be unique (no duplicate redo entries)

## Use Cases

1. **Failed Quiz**: A student failed a quiz and used all their attempts, but deserves another chance
2. **Technical Issues**: A student experienced technical difficulties during their quiz attempt
3. **Missed Deadline**: A student missed the quiz deadline due to valid reasons (illness, emergency, etc.)
4. **Administrative Decision**: Teacher or administrator decides to grant additional attempts for any reason

## Notes

- The redo entry is automatically deleted when the student successfully submits their redo attempt
- Only one redo entry can exist per student-quiz combination
- The endpoint validates that both the student and quiz exist before creating the redo entry
- This endpoint should typically be restricted to administrators or teachers (add appropriate guards as needed)

## Related Endpoints

After adding a redo entry, the student can:
1. See the quiz status as "redo" in the `getSubjectsAndLessonsByWeekId` endpoint
2. Submit a quiz answer with `redo: true` flag using the quiz submission endpoint
