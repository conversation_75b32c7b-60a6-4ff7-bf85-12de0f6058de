import { IsString, IsNotEmpty, IsInt, IsArray, ValidateNested, IsOptional } from 'class-validator';
import { Type } from 'class-transformer';

export class SubCategory {
  @IsInt()
  id: number;

  @IsString()
  @IsNotEmpty()
  name: string;
}

export class AddSubCategoryDto {
  @IsString()
  @IsNotEmpty()
  name: string;
}

export class UpdateSubCategoriesDto {
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => SubCategory)
  @IsOptional()
  subCategories?: SubCategory[];
}

export class DeleteSubCategoryDto {
  @IsInt()
  subCategoryId: number;
}
