import { Injectable, Logger, NotFoundException, HttpException, HttpStatus, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateQuizDto } from './dto/create-quiz.dto';
import { UpdateQuizDto } from './dto/update-quiz.dto';

@Injectable()
export class QuizService {
  private readonly logger = new Logger(QuizService.name);

  constructor(private prisma: PrismaService) {}

  async createQuiz(createQuizDto: CreateQuizDto) {
    try {
      // Verify that the subject exists
      const subject = await this.prisma.subject.findUnique({
        where: { id: createQuizDto.subjectId },
      });

      if (!subject) {
        throw new NotFoundException(`Subject with ID ${createQuizDto.subjectId} not found`);
      }

      // Semester is no longer used in the Quiz model

      // Verify that the lesson exists if provided
      if (createQuizDto.lessonId) {
        const lesson = await this.prisma.lesson.findUnique({
          where: { id: createQuizDto.lessonId },
        });

        if (!lesson) {
          throw new NotFoundException(`Lesson with ID ${createQuizDto.lessonId} not found`);
        }
      }

      // Create the quiz
      const quiz = await this.prisma.quiz.create({
        data: {
          name: createQuizDto.name,
          subjectId: createQuizDto.subjectId,
          lessonId: createQuizDto.lessonId || null,
          grade: createQuizDto.grade,
          type: createQuizDto.type,
          numberOfAttempts: createQuizDto.numberOfAttempts,
          timeLimit: createQuizDto.timeLimit,
          content: JSON.stringify(createQuizDto.content),
          isRecord: createQuizDto.isRecord,
          subCategory: createQuizDto.subCategory ? JSON.stringify(createQuizDto.subCategory) : null,
        },
        include: {
          subject: true,
          lesson: true,
        },
      });

      return {
        message: 'Quiz created successfully',
        data: quiz,
      };
    } catch (error) {
      this.logger.error(`Failed to create quiz: ${error.message}`);
      throw error;
    }
  }

  async getQuizzes(filters: { subjectId?: number; weekId?: number; lessonId?: number }) {
    try {
      const whereClause: any = {};

      if (filters.subjectId) {
        whereClause.subjectId = filters.subjectId;
      }

      if (filters.lessonId) {
        whereClause.lessonId = filters.lessonId;
      }

      let quizzes: any[] = [];

      if (filters.weekId) {
        // If weekId is provided, find quizzes through lessons that are associated with this week
        // First, get all lessons for this week
        const weekLessons = await this.prisma.weekLesson.findMany({
          where: { weekId: filters.weekId },
          select: { lessonId: true }
        });

        // Extract lesson IDs
        const lessonIds = weekLessons.map(wl => wl.lessonId);

        // Find quizzes for these lessons
        quizzes = await this.prisma.quiz.findMany({
          where: {
            lessonId: {
              in: lessonIds.length > 0 ? lessonIds : [-1] // Use -1 as a placeholder if no lessons found
            },
            ...whereClause // Include other filters
          },
          include: {
            subject: true,
            lesson: true
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      } else {
        // If no weekId, use the original query
        quizzes = await this.prisma.quiz.findMany({
          where: whereClause,
          include: {
            subject: true,
            lesson: true
          },
          orderBy: {
            createdAt: 'desc',
          },
        });
      }

      return {
        message: 'Quizzes retrieved successfully',
        data: quizzes.map(quiz => ({
          ...quiz,
          content: typeof quiz.content === 'string'
            ? JSON.parse(quiz.content)
            : quiz.content,
          subCategory: quiz.subCategory
            ? JSON.parse(quiz.subCategory as string)
            : null,
          subject: quiz.subject ? {
            ...quiz.subject,
            subCategories: quiz.subject.subCategories
              ? JSON.parse(quiz.subject.subCategories as string)
              : []
          } : quiz.subject
        })),
      };
    } catch (error) {
      throw new Error(`Failed to fetch quizzes: ${error.message}`);
    }
  }

  async getQuizById(id: number) {
    try {
      const quiz = await this.prisma.quiz.findUnique({
        where: { id },
        include: {
          subject: {
            select: {
              id: true,
              name: true,
              code: true,
              subCategories: true,
            }
          },
          lesson: {
            select: {
              id: true,
              name: true,
            }
          }
        }
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }

      // Parse the content from JSON string if needed
      const parsedContent = typeof quiz.content === 'string'
        ? JSON.parse(quiz.content)
        : quiz.content;

      // Add question IDs to each question if content is an array
      const contentWithIds = Array.isArray(parsedContent)
        ? parsedContent.map((question, index) => ({
            ...question,
            questionId: index
          }))
        : parsedContent;

      return {
        message: 'Quiz retrieved successfully',
        data: {
          ...quiz,
          content: contentWithIds,
          subCategory: quiz.subCategory
            ? JSON.parse(quiz.subCategory as string)
            : null,
          subject: quiz.subject ? {
            ...quiz.subject,
            subCategories: quiz.subject.subCategories
              ? JSON.parse(quiz.subject.subCategories as string)
              : []
          } : quiz.subject
        }
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch quiz: ${error.message}`);
      throw new HttpException(
        `Failed to fetch quiz: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async updateQuiz(id: number, updateQuizDto: UpdateQuizDto) {
    try {
      // Check if quiz exists
      const existingQuiz = await this.prisma.quiz.findUnique({
        where: { id },
      });

      if (!existingQuiz) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }

      // If updating subject, semester, or lesson, verify they exist
      if (updateQuizDto.subjectId) {
        const subject = await this.prisma.subject.findUnique({
          where: { id: updateQuizDto.subjectId },
        });
        if (!subject) {
          throw new NotFoundException(`Subject with ID ${updateQuizDto.subjectId} not found`);
        }
      }

      // Semester is no longer used in the Quiz model

      if (updateQuizDto.lessonId) {
        const lesson = await this.prisma.lesson.findUnique({
          where: { id: updateQuizDto.lessonId },
        });
        if (!lesson) {
          throw new NotFoundException(`Lesson with ID ${updateQuizDto.lessonId} not found`);
        }
      }

      // Update the quiz
      const updatedQuiz = await this.prisma.quiz.update({
        where: { id },
        data: {
          name: updateQuizDto.name,
          subjectId: updateQuizDto.subjectId,
          lessonId: updateQuizDto.lessonId,
          grade: updateQuizDto.grade,
          type: updateQuizDto.type,
          numberOfAttempts: updateQuizDto.numberOfAttempts,
          timeLimit: updateQuizDto.timeLimit,
          content: updateQuizDto.content ? JSON.stringify(updateQuizDto.content) : undefined,
          isRecord: updateQuizDto.isRecord,
          subCategory: updateQuizDto.subCategory ? JSON.stringify(updateQuizDto.subCategory) : undefined,
        },
        include: {
          subject: true,
          lesson: true,
        },
      });

      return {
        message: 'Quiz updated successfully',
        data: {
          ...updatedQuiz,
          content: typeof updatedQuiz.content === 'string'
            ? JSON.parse(updatedQuiz.content)
            : updatedQuiz.content,
          subCategory: updatedQuiz.subCategory
            ? JSON.parse(updatedQuiz.subCategory as string)
            : null,
          subject: updatedQuiz.subject ? {
            ...updatedQuiz.subject,
            subCategories: updatedQuiz.subject.subCategories
              ? JSON.parse(updatedQuiz.subject.subCategories as string)
              : []
          } : updatedQuiz.subject
        },
      };
    } catch (error) {
      this.logger.error(`Failed to update quiz: ${error.message}`);
      throw error;
    }
  }

  async deleteQuiz(id: number) {
    try {
      // Check if quiz exists
      const existingQuiz = await this.prisma.quiz.findUnique({
        where: { id },
      });

      if (!existingQuiz) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }

      // Delete the quiz
      await this.prisma.quiz.delete({
        where: { id },
      });

      return {
        message: 'Quiz deleted successfully',
      };
    } catch (error) {
      this.logger.error(`Failed to delete quiz: ${error.message}`);
      throw error;
    }
  }

  async addQuizToWeek(weekId: number, { quizId }: { quizId: number }) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId },
        include: {
          lessons: {
            select: {
              lessonId: true
            }
          }
        }
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Check if quiz exists
      const quiz = await this.prisma.quiz.findUnique({
        where: { id: quizId }
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${quizId} not found`);
      }

      // Get lesson IDs for this week
      const lessonIds = week.lessons.map(wl => wl.lessonId);

      if (lessonIds.length === 0) {
        throw new BadRequestException(`Week with ID ${weekId} has no lessons. Add lessons to the week first.`);
      }

      // If the quiz doesn't have a lessonId, assign it to the first lesson of the week
      if (!quiz.lessonId) {
        const updatedQuiz = await this.prisma.quiz.update({
          where: { id: quizId },
          data: { lessonId: lessonIds[0] }
        });

        return {
          message: `Quiz with ID ${quizId} added to week ${weekId} by assigning to lesson ${lessonIds[0]} successfully`,
          data: updatedQuiz
        };
      }
      // If the quiz has a lessonId but it's not part of this week, update it to the first lesson
      else if (!lessonIds.includes(quiz.lessonId)) {
        const updatedQuiz = await this.prisma.quiz.update({
          where: { id: quizId },
          data: { lessonId: lessonIds[0] }
        });

        return {
          message: `Quiz with ID ${quizId} added to week ${weekId} by updating lesson from ${quiz.lessonId} to ${lessonIds[0]} successfully`,
          data: updatedQuiz
        };
      }
      // If the quiz already has a lessonId that's part of this week, it's already associated
      else {
        return {
          message: `Quiz with ID ${quizId} is already associated with week ${weekId} through lesson ${quiz.lessonId}`,
          data: quiz
        };
      }
    } catch (error) {
      if (error instanceof NotFoundException || error instanceof BadRequestException) {
        throw error;
      }
      throw new Error(`Failed to add quiz to week: ${error.message}`);
    }
  }

  async removeQuizFromWeek(weekId: number, quizId: number) {
    try {
      // Check if the quiz exists
      const quiz = await this.prisma.quiz.findUnique({
        where: { id: quizId },
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${quizId} not found`);
      }

      // Check if the quiz has a lessonId
      if (!quiz.lessonId) {
        throw new NotFoundException(`Quiz with ID ${quizId} is not associated with any lesson`);
      }

      // Check if the lesson is part of this week
      const weekLesson = await this.prisma.weekLesson.findFirst({
        where: {
          weekId,
          lessonId: quiz.lessonId
        }
      });

      if (!weekLesson) {
        throw new NotFoundException(`Quiz with ID ${quizId} is not associated with week ${weekId}`);
      }

      // Set the lessonId to null to remove the association
      await this.prisma.quiz.update({
        where: { id: quizId },
        data: { lessonId: null }
      });

      return {
        message: `Quiz with ID ${quizId} removed from week ${weekId} successfully`,
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to remove quiz from week: ${error.message}`);
    }
  }

  async getQuizzesByWeekId(weekId: number) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId },
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Get all lessons for this week
      const weekLessons = await this.prisma.weekLesson.findMany({
        where: { weekId },
        select: { lessonId: true }
      });

      // Extract lesson IDs
      const lessonIds = weekLessons.map(wl => wl.lessonId);

      // Find quizzes for these lessons
      const quizzes = await this.prisma.quiz.findMany({
        where: {
          lessonId: {
            in: lessonIds.length > 0 ? lessonIds : [-1] // Use -1 as a placeholder if no lessons found
          }
        },
        include: {
          subject: true,
          lesson: true,
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      return {
        message: 'Quizzes retrieved successfully',
        data: quizzes.map((quiz, index) => ({
          ...quiz,
          order: index + 1, // Assign order based on index
          content: typeof quiz.content === 'string'
            ? JSON.parse(quiz.content)
            : quiz.content,
          subject: quiz.subject ? {
            ...quiz.subject,
            subCategories: quiz.subject.subCategories
              ? JSON.parse(quiz.subject.subCategories as string)
              : []
          } : quiz.subject
        }))
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch quizzes: ${error.message}`);
    }
  }

  async getSimpleQuizzesByWeekId(weekId: number) {
    try {
      // Check if week exists
      const week = await this.prisma.week.findUnique({
        where: { id: weekId },
      });

      if (!week) {
        throw new NotFoundException(`Week with ID ${weekId} not found`);
      }

      // Get all lessons for this week
      const weekLessons = await this.prisma.weekLesson.findMany({
        where: { weekId },
        select: { lessonId: true }
      });

      // Extract lesson IDs
      const lessonIds = weekLessons.map(wl => wl.lessonId);

      // Find quizzes for these lessons with simplified information
      const quizzes = await this.prisma.quiz.findMany({
        where: {
          lessonId: {
            in: lessonIds.length > 0 ? lessonIds : [-1] // Use -1 as a placeholder if no lessons found
          }
        },
        select: {
          id: true,
          name: true,
          type: true,
          grade: true,
          timeLimit: true,
          numberOfAttempts: true,
          isRecord: true,
          subject: {
            select: {
              name: true,
            },
          },
        },
        orderBy: {
          createdAt: 'desc',
        },
      });

      // Transform the response to flatten the structure
      const simplifiedQuizzes = quizzes.map((quiz, index) => ({
        id: quiz.id,
        name: quiz.name,
        type: quiz.type,
        grade: quiz.grade,
        timeLimit: quiz.timeLimit,
        numberOfAttempts: quiz.numberOfAttempts,
        isRecord: quiz.isRecord,
        subjectName: quiz.subject.name,
        order: index + 1, // Assign order based on index
      }));

      return {
        message: 'Quizzes retrieved successfully',
        data: simplifiedQuizzes
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      throw new Error(`Failed to fetch quizzes: ${error.message}`);
    }
  }

  async getSimpleQuizzesByLessonId(lessonId: number) {
    try {
      // Check if lesson exists
      const lesson = await this.prisma.lesson.findUnique({
        where: { id: lessonId },
      });

      if (!lesson) {
        throw new NotFoundException(`Lesson with ID ${lessonId} not found`);
      }

      // Get quizzes for the lesson with simplified information
      const quizzes = await this.prisma.quiz.findMany({
        where: { lessonId },
        select: {
          id: true,
          name: true,
          grade: true,
          type: true,
          timeLimit: true,
          numberOfAttempts: true,
          isRecord: true,
          subject: {
            select: {
              id: true,
              name: true,
              code: true,
              subCategories: true
            }
          }
        },
        orderBy: {
          createdAt: 'desc'
        }
      });

      // Parse subcategories for subjects
      const quizzesWithParsedSubCategories = quizzes.map(quiz => ({
        ...quiz,
        subject: quiz.subject ? {
          ...quiz.subject,
          subCategories: quiz.subject.subCategories
            ? JSON.parse(quiz.subject.subCategories as string)
            : []
        } : quiz.subject
      }));

      return {
        message: 'Quizzes retrieved successfully',
        data: quizzesWithParsedSubCategories
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch quizzes: ${error.message}`);
      throw new HttpException(
        `Failed to fetch quizzes: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  async getQuizWithRandomQuestions(id: number) {
    try {
      // Get the quiz with all data
      const quiz = await this.prisma.quiz.findUnique({
        where: { id },
        include: {
          subject: true,
          lesson: true
        }
      });

      if (!quiz) {
        throw new NotFoundException(`Quiz with ID ${id} not found`);
      }
      if (quiz.isRecord === true) {
        let allQuestions = [];
        try {
          allQuestions = typeof quiz.content === 'string'
        ? JSON.parse(quiz.content)
        : quiz.content;
        } catch (error) {
          this.logger.error(`Failed to parse quiz content: ${error.message}`);
          throw new HttpException(
        'Failed to parse quiz content',
        HttpStatus.INTERNAL_SERVER_ERROR
          );
        }

        if (!Array.isArray(allQuestions) || allQuestions.length === 0) {
          throw new HttpException(
        'No questions found for record quiz',
        HttpStatus.BAD_REQUEST
          );
        }

        // The only question will always be type 'record'
        const question = { ...allQuestions[0], type: 'record', grade: quiz.grade, questionId: 0 };

        return {
          message: 'Quiz retrieved successfully',
          data: {
        ...quiz,
        content: [question],
        subject: quiz.subject ? {
          ...quiz.subject,
          subCategories: quiz.subject.subCategories
            ? JSON.parse(quiz.subject.subCategories as string)
            : []
        } : quiz.subject
          }
        };
      }
      // Parse the content to get all questions
      let allQuestions = [];
      try {
        allQuestions = typeof quiz.content === 'string'
          ? JSON.parse(quiz.content)
          : quiz.content;
      } catch (error) {
        this.logger.error(`Failed to parse quiz content: ${error.message}`);
        throw new HttpException(
          'Failed to parse quiz content',
          HttpStatus.INTERNAL_SERVER_ERROR
        );
      }

      // Group questions by grade categories (1, 3, and 5)
      const questionsByGrade = {
        1: allQuestions.filter(q => q.grade === 1),
        3: allQuestions.filter(q => q.grade === 3),
        5: allQuestions.filter(q => q.grade === 5)
      };

      // Log the number of questions in each category
      this.logger.debug(`Questions by grade: Grade 1: ${questionsByGrade[1].length}, Grade 3: ${questionsByGrade[3].length}, Grade 5: ${questionsByGrade[5].length}`);

      // Select questions to match the total quiz grade
      const selectedQuestions = this.selectQuestionsToMatchGrade(questionsByGrade, quiz.grade);

      // Randomize the order of selected questions
      const randomizedQuestions = this.shuffleArray(selectedQuestions);

      // Add question IDs to each question
      const questionsWithIds = randomizedQuestions.map((question, index) => ({
        ...question,
        questionId: index
      }));

      // Calculate total grade of selected questions for verification
      const totalSelectedGrade = questionsWithIds.reduce((sum, q) => sum + q.grade, 0);
      this.logger.debug(`Total grade of selected questions: ${totalSelectedGrade}, Quiz grade: ${quiz.grade}`);

      return {
        message: 'Quiz retrieved successfully',
        data: {
          ...quiz,
          content: questionsWithIds,
          subject: quiz.subject ? {
            ...quiz.subject,
            subCategories: quiz.subject.subCategories
              ? JSON.parse(quiz.subject.subCategories as string)
              : []
          } : quiz.subject
        }
      };
    } catch (error) {
      if (error instanceof NotFoundException) {
        throw error;
      }
      this.logger.error(`Failed to fetch quiz: ${error.message}`);
      throw new HttpException(
        `Failed to fetch quiz: ${error.message}`,
        HttpStatus.INTERNAL_SERVER_ERROR
      );
    }
  }

  /**
   * Selects questions from different grade categories to match the total quiz grade
   * @param questionsByGrade Object containing questions grouped by grade (1, 3, 5)
   * @param targetGrade The total grade to match
   * @returns Array of selected questions
   */
  private selectQuestionsToMatchGrade(questionsByGrade: Record<number, any[]>, targetGrade: number): any[] {
    let remainingGrade = targetGrade;
    const selectedQuestions = [];

    // Try to find the optimal combination of questions
    // Start with the highest grade questions (5) and work down

    // Calculate how many grade 5 questions we need
    let grade5Count = Math.floor(remainingGrade / 5);
    // Make sure we don't select more than available
    grade5Count = Math.min(grade5Count, questionsByGrade[5].length);

    // Select random grade 5 questions
    if (grade5Count > 0) {
      const shuffled5 = this.shuffleArray(questionsByGrade[5]);
      selectedQuestions.push(...shuffled5.slice(0, grade5Count));
      remainingGrade -= grade5Count * 5;
    }

    // Calculate how many grade 3 questions we need
    let grade3Count = Math.floor(remainingGrade / 3);
    // Make sure we don't select more than available
    grade3Count = Math.min(grade3Count, questionsByGrade[3].length);

    // Select random grade 3 questions
    if (grade3Count > 0) {
      const shuffled3 = this.shuffleArray(questionsByGrade[3]);
      selectedQuestions.push(...shuffled3.slice(0, grade3Count));
      remainingGrade -= grade3Count * 3;
    }

    // Calculate how many grade 1 questions we need
    let grade1Count = Math.floor(remainingGrade / 1);
    // Make sure we don't select more than available
    grade1Count = Math.min(grade1Count, questionsByGrade[1].length);

    // Select random grade 1 questions
    if (grade1Count > 0) {
      const shuffled1 = this.shuffleArray(questionsByGrade[1]);
      selectedQuestions.push(...shuffled1.slice(0, grade1Count));
      remainingGrade -= grade1Count * 1;
    }

    // If we couldn't match the exact grade (due to not enough questions in certain categories)
    // Try alternative combinations
    if (remainingGrade > 0) {
      this.logger.debug(`Couldn't match exact grade, remaining: ${remainingGrade}. Trying alternative combinations.`);

      // Try to replace a grade 3 with three grade 1s
      if (remainingGrade >= 2 && selectedQuestions.some(q => q.grade === 3) && questionsByGrade[1].length >= 3) {
        // Remove one grade 3 question
        const grade3Index = selectedQuestions.findIndex(q => q.grade === 3);
        if (grade3Index !== -1) {
          selectedQuestions.splice(grade3Index, 1);
          remainingGrade += 3;

          // Add three grade 1 questions
          const unusedGrade1 = questionsByGrade[1].filter(q => !selectedQuestions.includes(q));
          const shuffledUnused1 = this.shuffleArray(unusedGrade1);
          selectedQuestions.push(...shuffledUnused1.slice(0, 3));
          remainingGrade -= 3;
        }
      }

      // Try to replace a grade 5 with a grade 3 and two grade 1s
      if (remainingGrade >= 2 && selectedQuestions.some(q => q.grade === 5) &&
          questionsByGrade[3].length >= 1 && questionsByGrade[1].length >= 2) {
        // Remove one grade 5 question
        const grade5Index = selectedQuestions.findIndex(q => q.grade === 5);
        if (grade5Index !== -1) {
          selectedQuestions.splice(grade5Index, 1);
          remainingGrade += 5;

          // Add one grade 3 question
          const unusedGrade3 = questionsByGrade[3].filter(q => !selectedQuestions.includes(q));
          const shuffledUnused3 = this.shuffleArray(unusedGrade3);
          if (shuffledUnused3.length > 0) {
            selectedQuestions.push(shuffledUnused3[0]);
            remainingGrade -= 3;
          }

          // Add two grade 1 questions
          const unusedGrade1 = questionsByGrade[1].filter(q => !selectedQuestions.includes(q));
          const shuffledUnused1 = this.shuffleArray(unusedGrade1);
          selectedQuestions.push(...shuffledUnused1.slice(0, 2));
          remainingGrade -= 2;
        }
      }
    }

    // If we still couldn't match the exact grade, log a warning
    if (remainingGrade > 0) {
      this.logger.warn(`Could not match exact grade. Target: ${targetGrade}, Actual: ${targetGrade - remainingGrade}`);
    }

    return selectedQuestions;
  }

  // Helper method to shuffle an array (Fisher-Yates algorithm)
  private shuffleArray<T>(array: T[]): T[] {
    const shuffled = [...array];
    for (let i = shuffled.length - 1; i > 0; i--) {
      const j = Math.floor(Math.random() * (i + 1));
      [shuffled[i], shuffled[j]] = [shuffled[j], shuffled[i]];
    }
    return shuffled;
  }
}








