-- CreateTable
CREATE TABLE "redos" (
    "id" SERIAL NOT NULL,
    "studentId" INTEGER NOT NULL,
    "quizId" INTEGER NOT NULL,
    "createdAt" TIMESTAMP(3) NOT NULL DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP(3) NOT NULL,

    CONSTRAINT "redos_pkey" PRIMARY KEY ("id")
);

-- CreateIndex
CREATE INDEX "redos_studentId_idx" ON "redos"("studentId");

-- CreateIndex
CREATE INDEX "redos_quizId_idx" ON "redos"("quizId");

-- AddForeignKey
ALTER TABLE "redos" ADD CONSTRAINT "redos_studentId_fkey" FOREIGN KEY ("studentId") REFERENCES "students"("id") ON DELETE RESTRICT ON UPDATE CASCADE;

-- Add<PERSON><PERSON>ign<PERSON>ey
ALTER TABLE "redos" ADD CONSTRAINT "redos_quizId_fkey" FOREIGN KEY ("quizId") REFERENCES "quiz"("id") ON DELETE RESTRICT ON UPDATE CASCADE;
